<?xml version="1.0" encoding="UTF-8"?>
<issues format="6" by="lint 8.9.1" type="baseline" client="gradle" dependencies="false" name="AGP (8.9.1)" variant="all" version="8.9.1">

    <issue
        id="OldTarget<PERSON><PERSON>"
        message="Not targeting the latest versions of Android; compatibility modes apply. Consider testing and updating this version. Consult the `android.os.Build.VERSION_CODES` javadoc for details."
        errorLine1="        targetSdk = 34"
        errorLine2="        ~~~~~~~~~~~~~~">
        <location
            file="build.gradle.kts"
            line="15"
            column="9"/>
    </issue>

    <issue
        id="FragmentTagUsage"
        message="Replace the &lt;fragment> tag with FragmentContainerView."
        errorLine1="    &lt;fragment"
        errorLine2="     ~~~~~~~~">
        <location
            file="src/main/res/layout/activity_main.xml"
            line="21"
            column="6"/>
    </issue>

    <issue
        id="RedundantLabel"
        message="Redundant label can be removed"
        errorLine1="            android:label=&quot;@string/app_name&quot;>"
        errorLine2="            ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~">
        <location
            file="src/main/AndroidManifest.xml"
            line="55"
            column="13"/>
    </issue>

    <issue
        id="SelectedPhotoAccess"
        message="Your app is currently not handling Selected Photos Access introduced in Android 14+"
        errorLine1="    &lt;uses-permission android:name=&quot;android.permission.READ_MEDIA_IMAGES&quot; />"
        errorLine2="                                   ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~">
        <location
            file="src/main/AndroidManifest.xml"
            line="10"
            column="36"/>
    </issue>

    <issue
        id="GradleDependency"
        message="A newer version of `compileSdkVersion` than 34 is available: 35"
        errorLine1="    compileSdk = 34"
        errorLine2="    ~~~~~~~~~~~~~~~">
        <location
            file="build.gradle.kts"
            line="10"
            column="5"/>
    </issue>

    <issue
        id="GradleDependency"
        message="A newer version of androidx.core:core-ktx than 1.12.0 is available: 1.16.0"
        errorLine1="coreKtx = &quot;1.12.0&quot;"
        errorLine2="          ~~~~~~~~">
        <location
            file="$HOME/Desktop/Android-Projects/Cant_Decide/v02/cant-decide/gradle/libs.versions.toml"
            line="5"
            column="11"/>
    </issue>

    <issue
        id="GradleDependency"
        message="A newer version of androidx.core:core-ktx than 1.12.0 is available: 1.16.0"
        errorLine1="coreKtx = &quot;1.12.0&quot;"
        errorLine2="          ~~~~~~~~">
        <location
            file="$HOME/Desktop/Android-Projects/Cant_Decide/v02/cant-decide/gradle/libs.versions.toml"
            line="5"
            column="11"/>
    </issue>

    <issue
        id="GradleDependency"
        message="A newer version of androidx.core:core-ktx than 1.12.0 is available: 1.16.0"
        errorLine1="coreKtx = &quot;1.12.0&quot;"
        errorLine2="          ~~~~~~~~">
        <location
            file="$HOME/Desktop/Android-Projects/Cant_Decide/v02/cant-decide/gradle/libs.versions.toml"
            line="5"
            column="11"/>
    </issue>

    <issue
        id="GradleDependency"
        message="A newer version of androidx.appcompat:appcompat than 1.6.1 is available: 1.7.0"
        errorLine1="appcompat = &quot;1.6.1&quot;"
        errorLine2="            ~~~~~~~">
        <location
            file="$HOME/Desktop/Android-Projects/Cant_Decide/v02/cant-decide/gradle/libs.versions.toml"
            line="9"
            column="13"/>
    </issue>

    <issue
        id="GradleDependency"
        message="A newer version of androidx.appcompat:appcompat than 1.6.1 is available: 1.7.0"
        errorLine1="appcompat = &quot;1.6.1&quot;"
        errorLine2="            ~~~~~~~">
        <location
            file="$HOME/Desktop/Android-Projects/Cant_Decide/v02/cant-decide/gradle/libs.versions.toml"
            line="9"
            column="13"/>
    </issue>

    <issue
        id="GradleDependency"
        message="A newer version of androidx.appcompat:appcompat than 1.6.1 is available: 1.7.0"
        errorLine1="appcompat = &quot;1.6.1&quot;"
        errorLine2="            ~~~~~~~">
        <location
            file="$HOME/Desktop/Android-Projects/Cant_Decide/v02/cant-decide/gradle/libs.versions.toml"
            line="9"
            column="13"/>
    </issue>

    <issue
        id="GradleDependency"
        message="A newer version of com.google.android.material:material than 1.11.0 is available: 1.12.0"
        errorLine1="material = &quot;1.11.0&quot;"
        errorLine2="           ~~~~~~~~">
        <location
            file="$HOME/Desktop/Android-Projects/Cant_Decide/v02/cant-decide/gradle/libs.versions.toml"
            line="10"
            column="12"/>
    </issue>

    <issue
        id="GradleDependency"
        message="A newer version of com.google.android.material:material than 1.11.0 is available: 1.12.0"
        errorLine1="material = &quot;1.11.0&quot;"
        errorLine2="           ~~~~~~~~">
        <location
            file="$HOME/Desktop/Android-Projects/Cant_Decide/v02/cant-decide/gradle/libs.versions.toml"
            line="10"
            column="12"/>
    </issue>

    <issue
        id="GradleDependency"
        message="A newer version of com.google.android.material:material than 1.11.0 is available: 1.12.0"
        errorLine1="material = &quot;1.11.0&quot;"
        errorLine2="           ~~~~~~~~">
        <location
            file="$HOME/Desktop/Android-Projects/Cant_Decide/v02/cant-decide/gradle/libs.versions.toml"
            line="10"
            column="12"/>
    </issue>

    <issue
        id="GradleDependency"
        message="A newer version of androidx.constraintlayout:constraintlayout than 2.1.4 is available: 2.2.1"
        errorLine1="constraintlayout = &quot;2.1.4&quot;"
        errorLine2="                   ~~~~~~~">
        <location
            file="$HOME/Desktop/Android-Projects/Cant_Decide/v02/cant-decide/gradle/libs.versions.toml"
            line="11"
            column="20"/>
    </issue>

    <issue
        id="GradleDependency"
        message="A newer version of androidx.constraintlayout:constraintlayout than 2.1.4 is available: 2.2.1"
        errorLine1="constraintlayout = &quot;2.1.4&quot;"
        errorLine2="                   ~~~~~~~">
        <location
            file="$HOME/Desktop/Android-Projects/Cant_Decide/v02/cant-decide/gradle/libs.versions.toml"
            line="11"
            column="20"/>
    </issue>

    <issue
        id="GradleDependency"
        message="A newer version of androidx.constraintlayout:constraintlayout than 2.1.4 is available: 2.2.1"
        errorLine1="constraintlayout = &quot;2.1.4&quot;"
        errorLine2="                   ~~~~~~~">
        <location
            file="$HOME/Desktop/Android-Projects/Cant_Decide/v02/cant-decide/gradle/libs.versions.toml"
            line="11"
            column="20"/>
    </issue>

    <issue
        id="GradleDependency"
        message="A newer version of androidx.lifecycle:lifecycle-livedata-ktx than 2.7.0 is available: 2.8.7"
        errorLine1="lifecycleLivedataKtx = &quot;2.7.0&quot;"
        errorLine2="                       ~~~~~~~">
        <location
            file="$HOME/Desktop/Android-Projects/Cant_Decide/v02/cant-decide/gradle/libs.versions.toml"
            line="12"
            column="24"/>
    </issue>

    <issue
        id="GradleDependency"
        message="A newer version of androidx.lifecycle:lifecycle-livedata-ktx than 2.7.0 is available: 2.8.7"
        errorLine1="lifecycleLivedataKtx = &quot;2.7.0&quot;"
        errorLine2="                       ~~~~~~~">
        <location
            file="$HOME/Desktop/Android-Projects/Cant_Decide/v02/cant-decide/gradle/libs.versions.toml"
            line="12"
            column="24"/>
    </issue>

    <issue
        id="GradleDependency"
        message="A newer version of androidx.lifecycle:lifecycle-livedata-ktx than 2.7.0 is available: 2.8.7"
        errorLine1="lifecycleLivedataKtx = &quot;2.7.0&quot;"
        errorLine2="                       ~~~~~~~">
        <location
            file="$HOME/Desktop/Android-Projects/Cant_Decide/v02/cant-decide/gradle/libs.versions.toml"
            line="12"
            column="24"/>
    </issue>

    <issue
        id="GradleDependency"
        message="A newer version of androidx.lifecycle:lifecycle-viewmodel-ktx than 2.7.0 is available: 2.8.7"
        errorLine1="lifecycleViewmodelKtx = &quot;2.7.0&quot;"
        errorLine2="                        ~~~~~~~">
        <location
            file="$HOME/Desktop/Android-Projects/Cant_Decide/v02/cant-decide/gradle/libs.versions.toml"
            line="13"
            column="25"/>
    </issue>

    <issue
        id="GradleDependency"
        message="A newer version of androidx.lifecycle:lifecycle-viewmodel-ktx than 2.7.0 is available: 2.8.7"
        errorLine1="lifecycleViewmodelKtx = &quot;2.7.0&quot;"
        errorLine2="                        ~~~~~~~">
        <location
            file="$HOME/Desktop/Android-Projects/Cant_Decide/v02/cant-decide/gradle/libs.versions.toml"
            line="13"
            column="25"/>
    </issue>

    <issue
        id="GradleDependency"
        message="A newer version of androidx.lifecycle:lifecycle-viewmodel-ktx than 2.7.0 is available: 2.8.7"
        errorLine1="lifecycleViewmodelKtx = &quot;2.7.0&quot;"
        errorLine2="                        ~~~~~~~">
        <location
            file="$HOME/Desktop/Android-Projects/Cant_Decide/v02/cant-decide/gradle/libs.versions.toml"
            line="13"
            column="25"/>
    </issue>

    <issue
        id="GradleDependency"
        message="A newer version of androidx.navigation:navigation-fragment-ktx than 2.7.7 is available: 2.8.9"
        errorLine1="navigationFragmentKtx = &quot;2.7.7&quot;"
        errorLine2="                        ~~~~~~~">
        <location
            file="$HOME/Desktop/Android-Projects/Cant_Decide/v02/cant-decide/gradle/libs.versions.toml"
            line="14"
            column="25"/>
    </issue>

    <issue
        id="GradleDependency"
        message="A newer version of androidx.navigation:navigation-fragment-ktx than 2.7.7 is available: 2.8.9"
        errorLine1="navigationFragmentKtx = &quot;2.7.7&quot;"
        errorLine2="                        ~~~~~~~">
        <location
            file="$HOME/Desktop/Android-Projects/Cant_Decide/v02/cant-decide/gradle/libs.versions.toml"
            line="14"
            column="25"/>
    </issue>

    <issue
        id="GradleDependency"
        message="A newer version of androidx.navigation:navigation-fragment-ktx than 2.7.7 is available: 2.8.9"
        errorLine1="navigationFragmentKtx = &quot;2.7.7&quot;"
        errorLine2="                        ~~~~~~~">
        <location
            file="$HOME/Desktop/Android-Projects/Cant_Decide/v02/cant-decide/gradle/libs.versions.toml"
            line="14"
            column="25"/>
    </issue>

    <issue
        id="GradleDependency"
        message="A newer version of androidx.navigation:navigation-ui-ktx than 2.7.7 is available: 2.8.9"
        errorLine1="navigationUiKtx = &quot;2.7.7&quot;"
        errorLine2="                  ~~~~~~~">
        <location
            file="$HOME/Desktop/Android-Projects/Cant_Decide/v02/cant-decide/gradle/libs.versions.toml"
            line="15"
            column="19"/>
    </issue>

    <issue
        id="GradleDependency"
        message="A newer version of androidx.navigation:navigation-ui-ktx than 2.7.7 is available: 2.8.9"
        errorLine1="navigationUiKtx = &quot;2.7.7&quot;"
        errorLine2="                  ~~~~~~~">
        <location
            file="$HOME/Desktop/Android-Projects/Cant_Decide/v02/cant-decide/gradle/libs.versions.toml"
            line="15"
            column="19"/>
    </issue>

    <issue
        id="GradleDependency"
        message="A newer version of androidx.navigation:navigation-ui-ktx than 2.7.7 is available: 2.8.9"
        errorLine1="navigationUiKtx = &quot;2.7.7&quot;"
        errorLine2="                  ~~~~~~~">
        <location
            file="$HOME/Desktop/Android-Projects/Cant_Decide/v02/cant-decide/gradle/libs.versions.toml"
            line="15"
            column="19"/>
    </issue>

    <issue
        id="GradleDependency"
        message="A newer version of com.google.firebase:firebase-bom than 32.8.0 is available: 33.12.0"
        errorLine1="firebaseBom = &quot;32.8.0&quot;"
        errorLine2="              ~~~~~~~~">
        <location
            file="$HOME/Desktop/Android-Projects/Cant_Decide/v02/cant-decide/gradle/libs.versions.toml"
            line="17"
            column="15"/>
    </issue>

    <issue
        id="GradleDependency"
        message="A newer version of com.google.firebase:firebase-bom than 32.8.0 is available: 33.12.0"
        errorLine1="firebaseBom = &quot;32.8.0&quot;"
        errorLine2="              ~~~~~~~~">
        <location
            file="$HOME/Desktop/Android-Projects/Cant_Decide/v02/cant-decide/gradle/libs.versions.toml"
            line="17"
            column="15"/>
    </issue>

    <issue
        id="GradleDependency"
        message="A newer version of com.google.firebase:firebase-bom than 32.8.0 is available: 33.12.0"
        errorLine1="firebaseBom = &quot;32.8.0&quot;"
        errorLine2="              ~~~~~~~~">
        <location
            file="$HOME/Desktop/Android-Projects/Cant_Decide/v02/cant-decide/gradle/libs.versions.toml"
            line="17"
            column="15"/>
    </issue>

    <issue
        id="GradleDependency"
        message="A newer version of com.google.firebase.crashlytics than 2.9.9 is available: 3.0.3"
        errorLine1="firebaseCrashlyticsPlugin = &quot;2.9.9&quot;"
        errorLine2="                            ~~~~~~~">
        <location
            file="$HOME/Desktop/Android-Projects/Cant_Decide/v02/cant-decide/gradle/libs.versions.toml"
            line="19"
            column="29"/>
    </issue>

    <issue
        id="GradleDependency"
        message="A newer version of com.google.firebase.crashlytics than 2.9.9 is available: 3.0.3"
        errorLine1="firebaseCrashlyticsPlugin = &quot;2.9.9&quot;"
        errorLine2="                            ~~~~~~~">
        <location
            file="$HOME/Desktop/Android-Projects/Cant_Decide/v02/cant-decide/gradle/libs.versions.toml"
            line="19"
            column="29"/>
    </issue>

    <issue
        id="GradleDependency"
        message="A newer version of com.google.firebase.crashlytics than 2.9.9 is available: 3.0.3"
        errorLine1="firebaseCrashlyticsPlugin = &quot;2.9.9&quot;"
        errorLine2="                            ~~~~~~~">
        <location
            file="$HOME/Desktop/Android-Projects/Cant_Decide/v02/cant-decide/gradle/libs.versions.toml"
            line="19"
            column="29"/>
    </issue>

    <issue
        id="GradleDependency"
        message="A newer version of androidx.compose:compose-bom than 2024.04.00 is available: 2025.04.00"
        errorLine1="composeBom = &quot;2024.04.00&quot;"
        errorLine2="             ~~~~~~~~~~~~">
        <location
            file="$HOME/Desktop/Android-Projects/Cant_Decide/v02/cant-decide/gradle/libs.versions.toml"
            line="21"
            column="14"/>
    </issue>

    <issue
        id="GradleDependency"
        message="A newer version of androidx.compose:compose-bom than 2024.04.00 is available: 2025.04.00"
        errorLine1="composeBom = &quot;2024.04.00&quot;"
        errorLine2="             ~~~~~~~~~~~~">
        <location
            file="$HOME/Desktop/Android-Projects/Cant_Decide/v02/cant-decide/gradle/libs.versions.toml"
            line="21"
            column="14"/>
    </issue>

    <issue
        id="GradleDependency"
        message="A newer version of androidx.compose:compose-bom than 2024.04.00 is available: 2025.04.00"
        errorLine1="composeBom = &quot;2024.04.00&quot;"
        errorLine2="             ~~~~~~~~~~~~">
        <location
            file="$HOME/Desktop/Android-Projects/Cant_Decide/v02/cant-decide/gradle/libs.versions.toml"
            line="21"
            column="14"/>
    </issue>

    <issue
        id="GradleDependency"
        message="A newer version of androidx.activity:activity-compose than 1.8.2 is available: 1.10.1"
        errorLine1="composeActivity = &quot;1.8.2&quot;"
        errorLine2="                  ~~~~~~~">
        <location
            file="$HOME/Desktop/Android-Projects/Cant_Decide/v02/cant-decide/gradle/libs.versions.toml"
            line="22"
            column="19"/>
    </issue>

    <issue
        id="GradleDependency"
        message="A newer version of androidx.activity:activity-compose than 1.8.2 is available: 1.10.1"
        errorLine1="composeActivity = &quot;1.8.2&quot;"
        errorLine2="                  ~~~~~~~">
        <location
            file="$HOME/Desktop/Android-Projects/Cant_Decide/v02/cant-decide/gradle/libs.versions.toml"
            line="22"
            column="19"/>
    </issue>

    <issue
        id="GradleDependency"
        message="A newer version of androidx.activity:activity-compose than 1.8.2 is available: 1.10.1"
        errorLine1="composeActivity = &quot;1.8.2&quot;"
        errorLine2="                  ~~~~~~~">
        <location
            file="$HOME/Desktop/Android-Projects/Cant_Decide/v02/cant-decide/gradle/libs.versions.toml"
            line="22"
            column="19"/>
    </issue>

    <issue
        id="GradleDependency"
        message="A newer version of androidx.navigation:navigation-compose than 2.7.7 is available: 2.8.9"
        errorLine1="composeNavigation = &quot;2.7.7&quot;"
        errorLine2="                    ~~~~~~~">
        <location
            file="$HOME/Desktop/Android-Projects/Cant_Decide/v02/cant-decide/gradle/libs.versions.toml"
            line="23"
            column="21"/>
    </issue>

    <issue
        id="GradleDependency"
        message="A newer version of androidx.navigation:navigation-compose than 2.7.7 is available: 2.8.9"
        errorLine1="composeNavigation = &quot;2.7.7&quot;"
        errorLine2="                    ~~~~~~~">
        <location
            file="$HOME/Desktop/Android-Projects/Cant_Decide/v02/cant-decide/gradle/libs.versions.toml"
            line="23"
            column="21"/>
    </issue>

    <issue
        id="GradleDependency"
        message="A newer version of androidx.navigation:navigation-compose than 2.7.7 is available: 2.8.9"
        errorLine1="composeNavigation = &quot;2.7.7&quot;"
        errorLine2="                    ~~~~~~~">
        <location
            file="$HOME/Desktop/Android-Projects/Cant_Decide/v02/cant-decide/gradle/libs.versions.toml"
            line="23"
            column="21"/>
    </issue>

    <issue
        id="Typos"
        message="&quot;cant&quot; is a common misspelling; did you mean &quot;cannot&quot; or &quot;can not&quot; or &quot;can&apos;t&quot;?"
        errorLine1="    &lt;string name=&quot;app_name&quot;>cant-decide&lt;/string>"
        errorLine2="                            ^">
        <location
            file="src/main/res/values/strings.xml"
            line="2"
            column="29"/>
    </issue>

    <issue
        id="PluralsCandidate"
        message="Formatting %d followed by words (&quot;seconds&quot;): This should probably be a plural rather than a string"
        errorLine1="    &lt;string name=&quot;resend_timer&quot;>Resend in %d seconds&lt;/string>"
        errorLine2="    ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~">
        <location
            file="src/main/res/values/strings.xml"
            line="20"
            column="5"/>
    </issue>

    <issue
        id="StaticFieldLeak"
        message="Do not place Android context classes in static fields (static reference to `FirebaseFirestore` which has field `context` pointing to `Context`); this is a memory leak"
        errorLine1="        private val firestore: FirebaseFirestore = Firebase.firestore"
        errorLine2="        ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~">
        <location
            file="src/main/java/com/example/cant_decide/firebase/FirebaseHelper.kt"
            line="26"
            column="9"/>
    </issue>

    <issue
        id="Overdraw"
        message="Possible overdraw: Root element paints background `@color/white` with a theme that also paints a background (inferred theme is `@style/Theme_Cantdecide_NoActionBar`)"
        errorLine1="    android:background=&quot;@color/white&quot;"
        errorLine2="    ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~">
        <location
            file="src/main/res/layout/activity_splash.xml"
            line="7"
            column="5"/>
    </issue>

    <issue
        id="UnusedResources"
        message="The resource `R.color.primary_light` appears to be unused"
        errorLine1="    &lt;color name=&quot;primary_light&quot;>#FF42A5F5&lt;/color>"
        errorLine2="           ~~~~~~~~~~~~~~~~~~~~">
        <location
            file="src/main/res/values/colors.xml"
            line="14"
            column="12"/>
    </issue>

    <issue
        id="UnusedResources"
        message="The resource `R.color.accent_light` appears to be unused"
        errorLine1="    &lt;color name=&quot;accent_light&quot;>#FF9575CD&lt;/color>"
        errorLine2="           ~~~~~~~~~~~~~~~~~~~">
        <location
            file="src/main/res/values/colors.xml"
            line="16"
            column="12"/>
    </issue>

    <issue
        id="UnusedResources"
        message="The resource `R.dimen.activity_horizontal_margin` appears to be unused"
        errorLine1="    &lt;dimen name=&quot;activity_horizontal_margin&quot;>16dp&lt;/dimen>"
        errorLine2="           ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~">
        <location
            file="src/main/res/values/dimens.xml"
            line="3"
            column="12"/>
    </issue>

    <issue
        id="UnusedResources"
        message="The resource `R.dimen.activity_vertical_margin` appears to be unused"
        errorLine1="    &lt;dimen name=&quot;activity_vertical_margin&quot;>16dp&lt;/dimen>"
        errorLine2="           ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~">
        <location
            file="src/main/res/values/dimens.xml"
            line="4"
            column="12"/>
    </issue>

    <issue
        id="UnusedResources"
        message="The resource `R.drawable.ic_dashboard_black_24dp` appears to be unused"
        errorLine1="&lt;vector xmlns:android=&quot;http://schemas.android.com/apk/res/android&quot;"
        errorLine2="^">
        <location
            file="src/main/res/drawable/ic_dashboard_black_24dp.xml"
            line="1"
            column="1"/>
    </issue>

    <issue
        id="UnusedResources"
        message="The resource `R.drawable.ic_edit_black_24dp` appears to be unused"
        errorLine1="&lt;vector xmlns:android=&quot;http://schemas.android.com/apk/res/android&quot;"
        errorLine2="^">
        <location
            file="src/main/res/drawable/ic_edit_black_24dp.xml"
            line="1"
            column="1"/>
    </issue>

    <issue
        id="UnusedResources"
        message="The resource `R.string.verification_code_hint` appears to be unused"
        errorLine1="    &lt;string name=&quot;verification_code_hint&quot;>Enter 6-digit code&lt;/string>"
        errorLine2="            ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~">
        <location
            file="src/main/res/values/strings.xml"
            line="18"
            column="13"/>
    </issue>

    <issue
        id="UnusedResources"
        message="The resource `R.string.error_saving_profile` appears to be unused"
        errorLine1="    &lt;string name=&quot;error_saving_profile&quot;>Error saving profile. Please try again.&lt;/string>"
        errorLine2="            ~~~~~~~~~~~~~~~~~~~~~~~~~~~">
        <location
            file="src/main/res/values/strings.xml"
            line="29"
            column="13"/>
    </issue>

    <issue
        id="UnusedResources"
        message="The resource `R.string.take_photo` appears to be unused"
        errorLine1="    &lt;string name=&quot;take_photo&quot;>Take Photo&lt;/string>"
        errorLine2="            ~~~~~~~~~~~~~~~~~">
        <location
            file="src/main/res/values/strings.xml"
            line="35"
            column="13"/>
    </issue>

    <issue
        id="UnusedResources"
        message="The resource `R.string.error_network` appears to be unused"
        errorLine1="    &lt;string name=&quot;error_network&quot;>Network error. Please check your connection.&lt;/string>"
        errorLine2="            ~~~~~~~~~~~~~~~~~~~~">
        <location
            file="src/main/res/values/strings.xml"
            line="45"
            column="13"/>
    </issue>

    <issue
        id="IconLocation"
        message="Found bitmap drawable `res/drawable/logo_heading.png` in densityless folder">
        <location
            file="src/main/res/drawable/logo_heading.png"/>
    </issue>

    <issue
        id="UseKtx"
        message="Use the KTX extension function `String.toUri` instead?"
        errorLine1="            val uri = Uri.parse(imageUri)"
        errorLine2="                      ~~~~~~~~~~~~~~~~~~~">
        <location
            file="src/main/java/com/example/cant_decide/util/ImageUtils.kt"
            line="71"
            column="23"/>
    </issue>

    <issue
        id="UseKtx"
        message="Use the KTX extension function `String.toUri` instead?"
        errorLine1="            val uri = Uri.parse(imageUriString)"
        errorLine2="                      ~~~~~~~~~~~~~~~~~~~~~~~~~">
        <location
            file="src/main/java/com/example/cant_decide/util/ImageUtils.kt"
            line="107"
            column="23"/>
    </issue>

    <issue
        id="UseKtx"
        message="Use the KTX extension property `View.isVisible` instead?"
        errorLine1="                    if (binding.tvErrorMessage.visibility == View.VISIBLE) {"
        errorLine2="                        ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~">
        <location
            file="src/main/java/com/example/cant_decide/ui/auth/VerificationActivity.kt"
            line="154"
            column="25"/>
    </issue>

    <issue
        id="LabelFor"
        message="Missing accessibility label: provide either a view with an `android:labelFor` that references this view or provide an `android:hint`"
        errorLine1="            &lt;EditText"
        errorLine2="             ~~~~~~~~">
        <location
            file="src/main/res/layout/activity_verification.xml"
            line="82"
            column="14"/>
    </issue>

    <issue
        id="LabelFor"
        message="Missing accessibility label: provide either a view with an `android:labelFor` that references this view or provide an `android:hint`"
        errorLine1="            &lt;EditText"
        errorLine2="             ~~~~~~~~">
        <location
            file="src/main/res/layout/activity_verification.xml"
            line="97"
            column="14"/>
    </issue>

    <issue
        id="LabelFor"
        message="Missing accessibility label: provide either a view with an `android:labelFor` that references this view or provide an `android:hint`"
        errorLine1="            &lt;EditText"
        errorLine2="             ~~~~~~~~">
        <location
            file="src/main/res/layout/activity_verification.xml"
            line="112"
            column="14"/>
    </issue>

    <issue
        id="LabelFor"
        message="Missing accessibility label: provide either a view with an `android:labelFor` that references this view or provide an `android:hint`"
        errorLine1="            &lt;EditText"
        errorLine2="             ~~~~~~~~">
        <location
            file="src/main/res/layout/activity_verification.xml"
            line="127"
            column="14"/>
    </issue>

    <issue
        id="LabelFor"
        message="Missing accessibility label: provide either a view with an `android:labelFor` that references this view or provide an `android:hint`"
        errorLine1="            &lt;EditText"
        errorLine2="             ~~~~~~~~">
        <location
            file="src/main/res/layout/activity_verification.xml"
            line="142"
            column="14"/>
    </issue>

    <issue
        id="LabelFor"
        message="Missing accessibility label: provide either a view with an `android:labelFor` that references this view or provide an `android:hint`"
        errorLine1="            &lt;EditText"
        errorLine2="             ~~~~~~~~">
        <location
            file="src/main/res/layout/activity_verification.xml"
            line="157"
            column="14"/>
    </issue>

    <issue
        id="HardcodedText"
        message="Hardcoded string &quot;No bio yet&quot;, should use `@string` resource"
        errorLine1="        android:text=&quot;No bio yet&quot;"
        errorLine2="        ~~~~~~~~~~~~~~~~~~~~~~~~~">
        <location
            file="src/main/res/layout/fragment_dashboard.xml"
            line="66"
            column="9"/>
    </issue>

    <issue
        id="HardcodedText"
        message="Hardcoded string &quot;Sign Out&quot;, should use `@string` resource"
        errorLine1="        android:text=&quot;Sign Out&quot;"
        errorLine2="        ~~~~~~~~~~~~~~~~~~~~~~~">
        <location
            file="src/main/res/layout/fragment_home.xml"
            line="29"
            column="9"/>
    </issue>

    <issue
        id="NullSafeMutableLiveData"
        message="Expected non-nullable value"
        errorLine1="                            _user.value = userProfile"
        errorLine2="                                          ~~~~~~~~~~~">
        <location
            file="src/main/java/com/example/cant_decide/ui/dashboard/DashboardViewModel.kt"
            line="115"
            column="43"/>
    </issue>

    <issue
        id="NullSafeMutableLiveData"
        message="Expected non-nullable value"
        errorLine1="                        _user.value = userProfile"
        errorLine2="                                      ~~~~~~~~~~~">
        <location
            file="src/main/java/com/example/cant_decide/ui/dashboard/DashboardViewModel.kt"
            line="119"
            column="39"/>
    </issue>

    <issue
        id="NullSafeMutableLiveData"
        message="Cannot set non-nullable LiveData value to `null`"
        errorLine1="        _error.value = null"
        errorLine2="                       ~~~~">
        <location
            file="src/main/java/com/example/cant_decide/ui/dashboard/DashboardViewModel.kt"
            line="238"
            column="24"/>
    </issue>

</issues>
