<?xml version="1.0" encoding="utf-8"?>
<manifest xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:tools="http://schemas.android.com/tools">

    <!-- Add Internet permission for Firebase -->
    <uses-permission android:name="android.permission.INTERNET" />
    <!-- Add network state permission for Firebase -->
    <uses-permission android:name="android.permission.ACCESS_NETWORK_STATE" />
    <!-- Media access for profile photos -->
    <uses-permission android:name="android.permission.READ_MEDIA_IMAGES" />
    <!-- Required for Android 14+ to allow users to select specific photos/videos -->
    <uses-permission android:name="android.permission.READ_MEDIA_VISUAL_USER_SELECTED" />
    <!-- For devices running Android 12 or lower -->
    <uses-permission android:name="android.permission.READ_EXTERNAL_STORAGE" android:maxSdkVersion="32" />
    <!-- Contacts access for finding friends -->
    <uses-permission android:name="android.permission.READ_CONTACTS" />

    <application
        android:name=".CantDecideApplication"
        android:allowBackup="true"
        android:dataExtractionRules="@xml/data_extraction_rules"
        android:fullBackupContent="@xml/backup_rules"
        android:icon="@mipmap/ic_launcher"
        android:label="@string/app_name"
        android:roundIcon="@mipmap/ic_launcher_round"
        android:supportsRtl="true"
        android:theme="@style/Theme.Cantdecide"
        tools:targetApi="31">

        <!-- Splash Screen Activity -->
        <activity
            android:name=".ui.splash.SplashActivity"
            android:exported="true"
            android:theme="@style/Theme.Cantdecide.NoActionBar">
            <intent-filter>
                <action android:name="android.intent.action.MAIN" />
                <category android:name="android.intent.category.LAUNCHER" />
            </intent-filter>
        </activity>

        <!-- Login Activity -->
        <activity
            android:name=".ui.auth.LoginActivity"
            android:exported="false"
            android:theme="@style/AppTheme.NoActionBar"
            android:windowSoftInputMode="adjustResize" />

        <!-- Verification Activity -->
        <activity
            android:name=".ui.auth.VerificationActivity"
            android:exported="false"
            android:theme="@style/AppTheme.NoActionBar"
            android:windowSoftInputMode="adjustResize" />

        <!-- Main Activity -->
        <activity
            android:name=".MainActivity"
            android:exported="true"
            android:theme="@style/Theme.Cantdecide"
            android:windowSoftInputMode="adjustResize" />

        <!-- Firebase Messaging Service -->
        <service
            android:name=".firebase.MyFirebaseMessagingService"
            android:exported="false">
            <intent-filter>
                <action android:name="com.google.firebase.MESSAGING_EVENT" />
            </intent-filter>
        </service>

    </application>

</manifest>