package app.donskey.cantdecide.data.model

import com.google.firebase.Timestamp
import java.util.Date // For Timestamp.now() if used directly, or just Timestamp itself

/**
 * Represents a single option in a poll.
 *
 * @property optionId Unique ID for the option (can be auto-generated client-side or by functions).
 * @property text The display text of the poll option.
 * @property voteCount The number of votes received for this option.
 */
data class PollOption(
    val optionId: String = "", 
    val text: String = "",
    val voteCount: Int = 0 // Added to store individual votes for this option
    // Consider adding voteCount: Int = 0 if useful for quick display later from aggregated data.
)

/**
 * Represents a text-based poll.
 *
 * @property pollId Firestore document ID for this poll.
 * @property creatorId UID of the user who created the poll.
 * @property creatorName Display name of the poll creator.
 * @property question The main question of the poll.
 * @property options A list of [PollOption] objects representing the choices.
 * @property allowedVoters List of User UIDs who are allowed to vote on this poll. Empty if open to all or handled by other means.
 * @property anonymous If true, voters' identities are not revealed with their votes.
 * @property type Discriminator for poll types, e.g., "TEXT_PRIVATE", "IMAGE_PUBLIC".
 * @property createdAt Timestamp of when the poll was created.
 * @property closesAt Optional timestamp for when the poll automatically closes. Null if no expiry.
 * @property status Current status of the poll (e.g., "ACTIVE", "CLOSED", "EXPIRED").
 * @property privacyLevel The privacy level of the poll, e.g., "private", "public".
 * @property voteCount Total number of votes cast on this poll.
 * @property votedBy List of user IDs who have voted on this poll.
 */
data class PrivateTextPoll(
    val pollId: String = "",
    val creatorId: String = "",
    val creatorName: String = "",
    val question: String = "",
    val options: List<PollOption> = emptyList(),
    val allowedVoters: List<String> = emptyList(),
    val anonymous: Boolean = false,
    val type: String = "text",
    val privacyLevel: String = "private",
    val createdAt: Timestamp = Timestamp(Date()), // Default to current time
    val closesAt: Timestamp? = null,
    val status: String = "ACTIVE",
    // The total number of votes this poll has received.
    val voteCount: Int = 0,
    // A list of user IDs who have already voted on this poll.
    val votedBy: List<String> = emptyList()
)

/**
 * Represents an individual vote cast on a poll.
 * This would typically be stored in a subcollection under the specific poll document.
 *
 * @property voteId Firestore document ID for this vote.
 * @property pollId ID of the poll this vote belongs to.
 * @property voterId UID of the user who cast the vote. May be anonymized or omitted depending on poll settings and storage strategy.
 * @property optionId ID of the [PollOption] that was chosen.
 * @property votedAt Timestamp of when the vote was cast.
 */
data class PollVote(
    val voteId: String = "",
    val pollId: String = "",
    val voterId: String = "", // If poll is not anonymous, this stores the UID.
                               // If anonymous, this might be an empty string or a placeholder if votes are still tracked individually.
    val optionId: String = "",
    val votedAt: Timestamp = Timestamp(Date()) // Default to current time
) 