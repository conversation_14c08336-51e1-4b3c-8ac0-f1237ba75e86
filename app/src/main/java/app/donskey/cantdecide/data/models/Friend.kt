package app.donskey.cantdecide.data.models

import com.google.firebase.Timestamp
import com.google.firebase.firestore.DocumentId
import com.google.firebase.firestore.PropertyName

/**
 * Data class representing a friendship relationship between two users.
 */
data class Friend(
    @DocumentId
    val id: String = "",
    
    @PropertyName("userId")
    val userId: String = "",
    
    @PropertyName("userId2")
    val userId2: String = "",
    
    @PropertyName("status")
    val status: String = "", // "pending", "accepted", "rejected", "canceled", "removed"
    
    @PropertyName("initiatedBy")
    val initiatedBy: String = "",
    
    @PropertyName("phoneNumber")
    val phoneNumber: String = "", // For users invited but not yet registered
    
    @PropertyName("groupId")
    val groupId: String? = null, // For future friend grouping
    
    @PropertyName("blocked")
    val blocked: Boolean = false,
    
    @PropertyName("createdAt")
    val createdAt: Timestamp = Timestamp.now(),
    
    @PropertyName("updatedAt")
    val updatedAt: Timestamp = Timestamp.now(),
    
    @PropertyName("lastInteraction")
    val lastInteraction: Timestamp = Timestamp.now(),
    
    @PropertyName("notificationEnabled")
    val notificationEnabled: Boolean = true,

    @PropertyName("participantVisibilities")
    val participantVisibilities: Map<String, Boolean> = emptyMap() // Stores UID to hideOnlineStatus (true if hidden)
) {
    // Computed property to determine the other user ID based on the current user ID
    fun getOtherUserId(currentUserId: String): String {
        return if (userId == currentUserId) userId2 else userId
    }
    
    // Helper method to check if this friendship is pending
    fun isPending(): Boolean = status == "pending"
    
    // Helper method to check if this friendship is accepted
    fun isAccepted(): Boolean = status == "accepted"
    
    // Helper method to check if the current user is the initiator
    fun isInitiatedBy(currentUserId: String): Boolean = initiatedBy == currentUserId
} 