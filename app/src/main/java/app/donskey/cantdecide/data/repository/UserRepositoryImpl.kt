package app.donskey.cantdecide.data.repository

import app.donskey.cantdecide.domain.repository.UserRepository
import com.google.firebase.auth.FirebaseAuth
import com.google.firebase.auth.FirebaseUser
import javax.inject.Inject
import javax.inject.Singleton

/**
 * Implementation of [UserRepository] that uses FirebaseAuth to retrieve user information.
 *
 * @property firebaseAuth Instance of [FirebaseAuth] for accessing authentication state.
 */
@Singleton // User repository is typically a singleton
class UserRepositoryImpl @Inject constructor(
    private val firebaseAuth: FirebaseAuth
) : UserRepository {

    /**
     * Retrieves the currently authenticated Firebase user from FirebaseAuth.
     *
     * @return The [FirebaseUser] object if a user is currently signed in, otherwise null.
     */
    override fun getCurrentUser(): FirebaseUser? {
        return firebaseAuth.currentUser
    }
} 