package app.donskey.cantdecide.data.repositories

import android.util.Log
import app.donskey.cantdecide.data.models.Friend
import app.donskey.cantdecide.data.models.User
import com.google.firebase.Timestamp
import com.google.firebase.auth.FirebaseAuth
import com.google.firebase.firestore.FieldValue
import com.google.firebase.firestore.FirebaseFirestore
import kotlinx.coroutines.tasks.await
import kotlinx.coroutines.flow.Flow
import kotlinx.coroutines.flow.combine
import kotlinx.coroutines.channels.awaitClose
import javax.inject.Inject
import javax.inject.Singleton

/**
 * Repository for managing friend relationships in Firestore.
 * Handles operations like sending/accepting friend requests and managing friend lists.
 */
@Singleton
class FriendsRepository @Inject constructor(
    private val auth: FirebaseAuth,
    private val firestore: FirebaseFirestore
) {

    companion object {
        private const val TAG = "FriendsRepository"
        private const val FRIENDS_COLLECTION = "friends"
        private const val USERS_COLLECTION = "users"
    }

    /**
     * Gets the current authenticated user ID.
     * @return String? The current user ID or null if not logged in
     */
    private fun getCurrentUserId(): String? {
        return auth.currentUser?.uid
    }

    /**
     * Gets a Flow emitting lists of all friends (accepted) for the current user.
     * This combines results where user is either userId or userId2 in a friendship.
     * @return Flow<List<Friend>> A flow emitting the list of friends with accepted status
     */
    fun getCurrentUserFriends(): Flow<List<Friend>> {
        val userId = getCurrentUserId() ?: return kotlinx.coroutines.flow.flowOf(emptyList())

        // Flow for friendships where current user is userId
        val flow1 = kotlinx.coroutines.flow.callbackFlow {
            val listener = firestore.collection(FRIENDS_COLLECTION)
                .whereEqualTo("userId", userId)
                .whereEqualTo("status", "accepted")
                .addSnapshotListener { snapshot, e ->
                    if (e != null) {
                        Log.e(TAG, "Error in friends flow 1: ${e.message}", e)
                        trySend(emptyList())
                        return@addSnapshotListener
                    }
                    val friends = snapshot?.documents?.mapNotNull { it.toObject(Friend::class.java) } ?: emptyList()
                    trySend(friends)
                }
            
            awaitClose { listener.remove() }
        }

        // Flow for friendships where current user is userId2
        val flow2 = kotlinx.coroutines.flow.callbackFlow {
            val listener = firestore.collection(FRIENDS_COLLECTION)
                .whereEqualTo("userId2", userId)
                .whereEqualTo("status", "accepted")
                .addSnapshotListener { snapshot, e ->
                    if (e != null) {
                        Log.e(TAG, "Error in friends flow 2: ${e.message}", e)
                        trySend(emptyList())
                        return@addSnapshotListener
                    }
                    val friends = snapshot?.documents?.mapNotNull { it.toObject(Friend::class.java) } ?: emptyList()
                    trySend(friends)
                }
            
            awaitClose { listener.remove() }
        }

        // Combine the results of the two flows
        return combine(flow1, flow2) { list1, list2 ->
            val combinedList = list1 + list2
            Log.d(TAG, "Combined friends list size: ${combinedList.size} for user $userId")
            combinedList
        }
    }

    /**
     * Gets a Flow emitting lists of incoming friend requests for the current user.
     * @return Flow<List<Friend>> A flow emitting the list of pending friend requests received
     */
    fun getIncomingFriendRequests(): Flow<List<Friend>> {
        val userId = getCurrentUserId() ?: return kotlinx.coroutines.flow.flowOf(emptyList())

        // Query for incoming requests
        val query = firestore.collection(FRIENDS_COLLECTION)
            .whereEqualTo("userId2", userId)
            .whereEqualTo("status", "pending")
            .whereNotEqualTo("initiatedBy", userId)

        // Return flow from snapshots
        return kotlinx.coroutines.flow.callbackFlow {
            val listener = query.addSnapshotListener { snapshot, e ->
                if (e != null) {
                    Log.e(TAG, "Error getting incoming friend requests flow: ${e.message}", e)
                    trySend(emptyList())
                    return@addSnapshotListener
                }
                val requests = snapshot?.documents?.mapNotNull { it.toObject(Friend::class.java) } ?: emptyList()
                Log.d(TAG, "Incoming requests snapshot size: ${requests.size} for user $userId")
                trySend(requests)
            }
            
            awaitClose { listener.remove() }
        }
    }

    /**
     * Gets a Flow emitting lists of outgoing friend requests (status: pending) from the current user.
     * @return Flow<List<Friend>> A flow emitting the list of pending friend requests sent by the current user.
     */
    fun getOutgoingPendingRequestsFlow(): Flow<List<Friend>> {
        val userId = getCurrentUserId() ?: return kotlinx.coroutines.flow.flowOf(emptyList())

        // Query for outgoing requests initiated by the current user that are pending
        val query = firestore.collection(FRIENDS_COLLECTION)
            .whereEqualTo("initiatedBy", userId) // Corrected field for sender
            .whereEqualTo("status", "pending")

        // Return flow from snapshots
        return kotlinx.coroutines.flow.callbackFlow {
            val listener = query.addSnapshotListener { snapshot, e ->
                if (e != null) {
                    Log.e(TAG, "Error getting outgoing pending requests flow: ${e.message}", e)
                    trySend(emptyList())
                    return@addSnapshotListener
                }
                val requests = snapshot?.documents?.mapNotNull { it.toObject(Friend::class.java) } ?: emptyList()
                Log.d(TAG, "Outgoing pending requests snapshot size: ${requests.size} for user $userId")
                trySend(requests)
            }
            
            awaitClose { listener.remove() }
        }
    }

    /**
     * Searches for users by phone number to add as friends.
     * @param phoneNumber String The phone number to search for
     * @return User? The found user or null if not found
     */
    suspend fun searchUserByPhoneNumber(phoneNumber: String): User? {
        try {
            // Normalize the phone number by removing spaces, etc.
            val normalizedPhoneNumber = phoneNumber.replace("\\s".toRegex(), "")
            
            val userQuery = firestore.collection(USERS_COLLECTION)
                .whereEqualTo("phoneNumber", normalizedPhoneNumber)
                .limit(1)
                .get()
                .await()

            if (!userQuery.isEmpty) {
                val userDoc = userQuery.documents[0]
                val user = userDoc.toObject(User::class.java)
                Log.d(TAG, "Found user with phone number $phoneNumber: ${user?.displayName}")
                return user
            } else {
                Log.d(TAG, "No user found with phone number $phoneNumber")
                return null
            }
        } catch (e: Exception) {
            Log.e(TAG, "Error searching user by phone number: ${e.message}", e)
            return null
        }
    }

    /**
     * Checks if a friendship (in any state) exists between two users.
     * @param userId1 String The first user ID
     * @param userId2 String The second user ID
     * @return Friend? The existing friendship or null if none exists
     */
    suspend fun checkExistingFriendship(userId1: String, userId2: String): Friend? {
        try {
            // Check both directions of friendship
            val query1 = firestore.collection(FRIENDS_COLLECTION)
                .whereEqualTo("userId", userId1)
                .whereEqualTo("userId2", userId2)
                .limit(1)
                .get()
                .await()

            if (!query1.isEmpty) {
                return query1.documents[0].toObject(Friend::class.java)
            }

            val query2 = firestore.collection(FRIENDS_COLLECTION)
                .whereEqualTo("userId", userId2)
                .whereEqualTo("userId2", userId1)
                .limit(1)
                .get()
                .await()

            if (!query2.isEmpty) {
                return query2.documents[0].toObject(Friend::class.java)
            }

            return null
        } catch (e: Exception) {
            Log.e(TAG, "Error checking existing friendship: ${e.message}", e)
            return null
        }
    }

    /**
     * Finds an existing friendship document between two users, regardless of state.
     * Checks both (userId1, userId2) and (userId2, userId1) combinations.
     * Requires composite indexes: (userId, userId2) and (userId2, userId).
     *
     * @param userId1 The ID of the first user.
     * @param userId2 The ID of the second user.
     * @return Friend? The found friendship document (as Friend object) or null if none exists or on error.
     */
    suspend fun findExistingFriendship(userId1: String, userId2: String): Friend? {
         if (userId1.isBlank() || userId2.isBlank()) {
            Log.w(TAG, "Cannot find existing friendship with blank user IDs.")
            return null
        }
        Log.d(TAG, "Checking for existing friendship between $userId1 and $userId2")
        return try {
            // Query 1: Check if userId1 is 'userId' and userId2 is 'userId2'
            val query1 = firestore.collection(FRIENDS_COLLECTION)
                .whereEqualTo("userId", userId1)
                .whereEqualTo("userId2", userId2)
                .limit(1)
                .get()
                .await()

            if (!query1.isEmpty) {
                 Log.d(TAG, "Found existing friendship (Query 1)")
                return query1.documents[0].toObject(Friend::class.java)
            }

            // Query 2: Check if userId1 is 'userId2' and userId2 is 'userId'
            val query2 = firestore.collection(FRIENDS_COLLECTION)
                .whereEqualTo("userId", userId2) // Swapped
                .whereEqualTo("userId2", userId1) // Swapped
                .limit(1)
                .get()
                .await()
            
             if (!query2.isEmpty) {
                Log.d(TAG, "Found existing friendship (Query 2)")
                return query2.documents[0].toObject(Friend::class.java)
            }

            // No friendship found in either direction
            Log.d(TAG, "No existing friendship found between $userId1 and $userId2")
            null
        } catch (e: Exception) {
            Log.e(TAG, "Error finding existing friendship between $userId1 and $userId2: ${e.message}", e)
            null // Return null on error
        }
    }

    /**
     * Sends a friend request to another user.
     * @param receiverId String The ID of the user to send the request to
     * @param receiverPhoneNumber String The phone number of the receiver (for non-app users)
     * @return Boolean True if successful, false otherwise
     */
    suspend fun sendFriendRequest(receiverId: String, receiverPhoneNumber: String = ""): Boolean {
        val senderId = getCurrentUserId() ?: return false

        // Don't allow sending requests to yourself
        if (senderId == receiverId) {
            Log.w(TAG, "Cannot send friend request to yourself")
            return false
        }

        try {
            // Check if a friendship already exists
            val existingFriendship = checkExistingFriendship(senderId, receiverId)
            if (existingFriendship != null) {
                Log.w(TAG, "Friendship already exists between $senderId and $receiverId with status: ${existingFriendship.status}")
                return false
            }

            // Create new friend request document
            val friendRequest = Friend(
                userId = senderId,
                userId2 = receiverId,
                status = "pending",
                initiatedBy = senderId,
                phoneNumber = receiverPhoneNumber,
                createdAt = Timestamp.now(),
                updatedAt = Timestamp.now(),
                lastInteraction = Timestamp.now(),
                notificationEnabled = true,
                blocked = false
            )

            // Add to Firestore
            val docRef = firestore.collection(FRIENDS_COLLECTION).document()
            docRef.set(friendRequest).await()

            // Temporarily comment out the receiver count update - Requires Cloud Function
            /*
            firestore.collection(USERS_COLLECTION)
                .document(receiverId)
                .update("pendingRequestCount", FieldValue.increment(1))
                .await()
            */

            Log.d(TAG, "Friend request sent from $senderId to $receiverId")
            return true
        } catch (e: Exception) {
            Log.e(TAG, "Error sending friend request: ${e.message}", e)
            return false
        }
    }

    /**
     * Accepts a friend request.
     * @param friendshipId String The ID of the friendship document
     * @return Boolean True if successful, false otherwise
     */
    suspend fun acceptFriendRequest(friendshipId: String): Boolean {
        val currentUserId = getCurrentUserId() ?: return false

        try {
            // Get the friendship document
            val friendshipDoc = firestore.collection(FRIENDS_COLLECTION)
                .document(friendshipId)
                .get()
                .await()

            val friendship = friendshipDoc.toObject(Friend::class.java) ?: return false

            // Verify that the current user is the receiver
            if (friendship.userId2 != currentUserId || friendship.status != "pending") {
                Log.w(TAG, "Cannot accept friend request: User is not the request recipient or status is not pending")
                return false
            }

            // Update the friendship status ONLY
            firestore.collection(FRIENDS_COLLECTION)
                .document(friendshipId)
                .update("status", "accepted") // Only update status
                .await()

            // Update counts for both users
            // Temporarily disable count updates due to restrictive security rules
            /*
            // Decrease pending count for receiver (current user)
            firestore.collection(USERS_COLLECTION)
                .document(currentUserId)
                .update("pendingRequestCount", FieldValue.increment(-1))
                .await()

            // Increase friends count for both users
            firestore.collection(USERS_COLLECTION)
                .document(currentUserId)
                .update("friendsCount", FieldValue.increment(1))
                .await()

            firestore.collection(USERS_COLLECTION)
                .document(friendship.userId)
                .update("friendsCount", FieldValue.increment(1))
                .await()
            */

            Log.d(TAG, "Friend request accepted: $friendshipId")
            return true
        } catch (e: Exception) {
            Log.e(TAG, "Error accepting friend request: ${e.message}", e)
            return false
        }
    }

    /**
     * Rejects a friend request.
     * @param friendshipId String The ID of the friendship document
     * @return Boolean True if successful, false otherwise
     */
    suspend fun rejectFriendRequest(friendshipId: String): Boolean {
        val currentUserId = getCurrentUserId() ?: return false

        try {
            // Get the friendship document
            val friendshipDoc = firestore.collection(FRIENDS_COLLECTION)
                .document(friendshipId)
                .get()
                .await()

            val friendship = friendshipDoc.toObject(Friend::class.java) ?: return false

            // Verify that the current user is the receiver
            if (friendship.userId2 != currentUserId || friendship.status != "pending") {
                Log.w(TAG, "Cannot reject friend request: User is not the request recipient or status is not pending")
                return false
            }

            // Update the friendship status ONLY
            firestore.collection(FRIENDS_COLLECTION)
                .document(friendshipId)
                .update("status", "rejected") // Only update status
                .await()

            // Decrease pending count for receiver (current user)
            // Temporarily disable count updates due to restrictive security rules
            /*
            firestore.collection(USERS_COLLECTION)
                .document(currentUserId)
                .update("pendingRequestCount", FieldValue.increment(-1))
                .await()
            */

            Log.d(TAG, "Friend request rejected: $friendshipId")
            return true
        } catch (e: Exception) {
            Log.e(TAG, "Error rejecting friend request: ${e.message}", e)
            return false
        }
    }

    /**
     * Cancels a sent friend request.
     * @param friendshipId String The ID of the friendship document
     * @return Boolean True if successful, false otherwise
     */
    suspend fun cancelFriendRequest(friendshipId: String): Boolean {
        val currentUserId = getCurrentUserId() ?: return false

        try {
            // Get the friendship document
            val friendshipDoc = firestore.collection(FRIENDS_COLLECTION)
                .document(friendshipId)
                .get()
                .await()

            val friendship = friendshipDoc.toObject(Friend::class.java) ?: return false

            // Verify that the current user is the sender
            if (friendship.userId != currentUserId || 
                friendship.initiatedBy != currentUserId || 
                friendship.status != "pending") {
                Log.w(TAG, "Cannot cancel friend request: User is not the request sender or status is not pending")
                return false
            }

            // Set status to canceled
            firestore.collection(FRIENDS_COLLECTION)
                .document(friendshipId)
                .update(
                    mapOf(
                        "status" to "canceled",
                        "updatedAt" to Timestamp.now()
                    )
                )
                .await()

            // Decrease pending count for receiver
            firestore.collection(USERS_COLLECTION)
                .document(friendship.userId2)
                .update("pendingRequestCount", FieldValue.increment(-1))
                .await()

            Log.d(TAG, "Friend request canceled: $friendshipId")
            return true
        } catch (e: Exception) {
            Log.e(TAG, "Error canceling friend request: ${e.message}", e)
            return false
        }
    }

    /**
     * Removes a friend relationship.
     * @param friendshipId String The ID of the friendship document
     * @return Boolean True if successful, false otherwise
     */
    suspend fun removeFriend(friendshipId: String): Boolean {
        val currentUserId = getCurrentUserId() ?: return false

        try {
            // Get the friendship document
            val friendshipDoc = firestore.collection(FRIENDS_COLLECTION)
                .document(friendshipId)
                .get()
                .await()

            val friendship = friendshipDoc.toObject(Friend::class.java) ?: return false

            // Verify that the current user is part of the friendship and status is accepted
            if ((friendship.userId != currentUserId && friendship.userId2 != currentUserId) || 
                friendship.status != "accepted") {
                Log.w(TAG, "Cannot remove friendship: User is not part of the friendship or status is not accepted")
                return false
            }

            // Get the other user's ID
            // val otherUserId = if (friendship.userId == currentUserId) friendship.userId2 else friendship.userId // Commented out as unused

            // Set status to removed ONLY
            firestore.collection(FRIENDS_COLLECTION)
                .document(friendshipId)
                .update("status", "removed") // Only update status
                // .update(
                //     mapOf(
                //         "status" to "removed",
                //         "updatedAt" to Timestamp.now()
                //     )
                // )
                .await()

            // Decrease friends count for both users
            // Temporarily disable count updates
            /*
            firestore.collection(USERS_COLLECTION)
                .document(currentUserId)
                .update("friendsCount", FieldValue.increment(-1))
                .await()

            firestore.collection(USERS_COLLECTION)
                .document(otherUserId) // Reference removed
                .update("friendsCount", FieldValue.increment(-1))
                .await()
            */

            Log.d(TAG, "Friend removed: $friendshipId")
            return true
        } catch (e: Exception) {
            Log.e(TAG, "Error removing friend: ${e.message}", e)
            return false
        }
    }

    /**
     * Toggles notifications for a specific friendship.
     * @param friendshipId String The ID of the friendship document
     * @param enabled Boolean Whether notifications should be enabled or disabled
     * @return Boolean True if successful, false otherwise
     */
    suspend fun toggleFriendNotifications(friendshipId: String, enabled: Boolean): Boolean {
        val currentUserId = getCurrentUserId() ?: return false

        try {
            // Get the friendship document
            val friendshipDoc = firestore.collection(FRIENDS_COLLECTION)
                .document(friendshipId)
                .get()
                .await()

            val friendship = friendshipDoc.toObject(Friend::class.java) ?: return false

            // Verify that the current user is part of the friendship
            if (friendship.userId != currentUserId && friendship.userId2 != currentUserId) {
                Log.w(TAG, "Cannot toggle notifications: User is not part of the friendship")
                return false
            }

            // Update notification setting
            firestore.collection(FRIENDS_COLLECTION)
                .document(friendshipId)
                .update("notificationEnabled", enabled)
                .await()

            Log.d(TAG, "Friend notifications updated: $friendshipId, enabled: $enabled")
            return true
        } catch (e: Exception) {
            Log.e(TAG, "Error toggling friend notifications: ${e.message}", e)
            return false
        }
    }
} 