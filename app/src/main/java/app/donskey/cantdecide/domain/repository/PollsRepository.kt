package app.donskey.cantdecide.domain.repository

import app.donskey.cantdecide.data.model.PrivateTextPoll
import app.donskey.cantdecide.data.model.PollVote
// import com.donskey.cantdecide.data.model.TextPoll // This line was incorrect and is now removed.
import app.donskey.cantdecide.util.Resource
import kotlinx.coroutines.flow.Flow
// Consider a generic Result class or use <PERSON><PERSON><PERSON>'s built-in Result
import app.donskey.cantdecide.ui.poll_details.InvitedFriendVoteStatusViewData

/**
 * Repository interface for poll-related data operations.
 */
interface PollsRepository {

    /**
     * Creates a new text-based poll in the data store.
     *
     * @param poll The [PrivateTextPoll] object to be created.
     * @return A [Resource] indicating success (wrapping pollId) or failure.
     */
    suspend fun createTextPoll(poll: PrivateTextPoll): Resource<String>

    // Future methods:
    // suspend fun getPollById(pollId: String): Result<PrivateTextPoll?>
    // fun getPollsForUser(userId: String): kotlinx.coroutines.flow.Flow<List<PrivateTextPoll>>
    // suspend fun addVote(pollId: String, vote: PollVote): Result<Unit>
    // suspend fun deletePoll(pollId: String): Result<Unit>

    suspend fun createPrivateTextPoll(poll: PrivateTextPoll): Resource<String>

    // Fetches private text polls created by the specified user.
    fun getPrivateTextPollsCreatedByCurrentUser(userId: String): Flow<List<PrivateTextPoll>>

    // Fetches private text polls where the specified user is an allowed voter.
    fun getPrivateTextPollsInvitedToCurrentUser(userId: String): Flow<List<PrivateTextPoll>>

    // TODO: Add methods for other poll types and operations (view, vote, delete, history)

    /**
     * Fetches a real-time flow of a specific poll's details.
     *
     * @param pollId The ID of the poll to fetch.
     * @return A [Flow] emitting [Resource] wrapping the [PrivateTextPoll] on success (or null if not found/deleted), or an error.
     */
    fun getPollDetails(pollId: String): Flow<Resource<PrivateTextPoll?>>

    /**
     * Fetches a user's specific vote on a given poll, if it exists.
     *
     * @param pollId The ID of the poll.
     * @param userId The ID of the user whose vote is to be fetched.
     * @return A [Resource] wrapping the [PollVote] if found (or null if not found), or an error.
     */
    suspend fun getUserVote(pollId: String, userId: String): Resource<PollVote?>

    /**
     * Submits a user's vote for a poll.
     *
     * @param vote The [PollVote] object representing the vote to be submitted.
     * @return A [Resource] indicating success or failure of the operation.
     */
    suspend fun submitVote(vote: PollVote): Resource<Unit>

    /**
     * Deletes a poll and all its associated data (e.g., votes) from the data store.
     *
     * @param pollId The ID of the poll to be deleted.
     * @return A [Resource] indicating success or failure of the deletion operation.
     */
    suspend fun deletePoll(pollId: String): Resource<Unit>

    // Fetches the voting status of friends invited to a specific poll.
    // This is intended to be used by the poll creator to see who has voted and for what.
    fun getInvitedFriendsVoteStatus(pollId: String): Flow<Resource<List<InvitedFriendVoteStatusViewData>>>
} 