package app.donskey.cantdecide.domain.repository

import com.google.firebase.auth.FirebaseUser

/**
 * Interface for accessing user-related data.
 */
interface UserRepository {
    /**
     * Retrieves the currently authenticated Firebase user.
     *
     * @return The [FirebaseUser] object if a user is currently signed in, otherwise null.
     */
    fun getCurrentUser(): FirebaseUser?

    // TODO: Add other user-related methods here as needed, e.g.:
    // suspend fun getUserProfile(userId: String): Resource<UserProfile?>
    // suspend fun updateUserProfile(userProfile: UserProfile): Resource<Unit>
} 