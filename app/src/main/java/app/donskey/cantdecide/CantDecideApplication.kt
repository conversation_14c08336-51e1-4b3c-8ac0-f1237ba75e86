package app.donskey.cantdecide

import android.app.Application
import android.util.Log
import androidx.lifecycle.Lifecycle
import androidx.lifecycle.LifecycleEventObserver
import androidx.lifecycle.LifecycleOwner
import androidx.lifecycle.ProcessLifecycleOwner
import coil.ImageLoader
import coil.ImageLoaderFactory
import coil.disk.DiskCache
import coil.memory.MemoryCache
import coil.request.CachePolicy
import coil.util.DebugLogger
import com.google.firebase.FirebaseApp
import com.google.firebase.appcheck.AppCheckProviderFactory
import com.google.firebase.appcheck.debug.DebugAppCheckProviderFactory
import com.google.firebase.appcheck.ktx.appCheck
import com.google.firebase.appcheck.playintegrity.PlayIntegrityAppCheckProviderFactory
import com.google.firebase.auth.FirebaseAuth
import com.google.firebase.auth.FirebaseUser
import com.google.firebase.firestore.FirebaseFirestore
import com.google.firebase.ktx.Firebase
import dagger.hilt.android.HiltAndroidApp
import kotlinx.coroutines.CoroutineScope
import kotlinx.coroutines.Dispatchers
import kotlinx.coroutines.launch
import kotlinx.coroutines.tasks.await
import okhttp3.OkHttpClient
import java.util.concurrent.TimeUnit

/**
 * Base Application class for CantDecide.
 * Annotated with @HiltAndroidApp to enable Hilt dependency injection.
 * Consolidates Debug and Release specific initializations.
 */
@HiltAndroidApp
class CantDecideApplication : Application(), ImageLoaderFactory, LifecycleEventObserver {

    companion object {
        private val TAG = if (BuildConfig.DEBUG) "CantDecideApp_DEBUG" else "CantDecideApp_RELEASE"
        private const val USERS_COLLECTION = "users"
        // private var previouslyVerifiedEmail: String? = null // Consider if still needed and how to manage
        var isAppInForeground: Boolean = false
            private set
    }

    private val appScope = CoroutineScope(Dispatchers.IO)
    private lateinit var auth: FirebaseAuth

    /**
     * Called when the application is starting, before any other application objects have been created.
     * Use this method to perform global initializations.
     */
    override fun onCreate() {
        super.onCreate()
        logInfo("Application onCreate started")

        ProcessLifecycleOwner.get().lifecycle.addObserver(this)

        try {
            FirebaseApp.initializeApp(this)
            logInfo("FirebaseApp initialized successfully.")

            // Initialize App Check based on build type
            val appCheckProviderFactory: AppCheckProviderFactory = if (BuildConfig.DEBUG) {
                logDebug("Initializing App Check with DEBUG provider.")
                DebugAppCheckProviderFactory.getInstance()
            } else {
                logInfo("Initializing App Check with PLAY INTEGRITY provider.")
                PlayIntegrityAppCheckProviderFactory.getInstance()
            }
            Firebase.appCheck.installAppCheckProviderFactory(appCheckProviderFactory, true)

            auth = FirebaseAuth.getInstance()
            initializeAuthFeatures()

        } catch (e: Exception) {
            logError("Error initializing Firebase: ${e.message}", e)
        }

        logInfo("Application onCreate finished")
    }

    private fun initializeAuthFeatures() {
        // Set persistence behavior
        try {
            val currentUser = auth.currentUser
            if (currentUser != null) {
                appScope.launch {
                    try {
                        logDebug("Refreshing auth token for persistence")
                        currentUser.reload().await()
                        currentUser.getIdToken(true).await() // Force refresh
                    } catch (e: Exception) {
                        logError("Error refreshing token: ${e.message}", e)
                    }
                }
            }
            logInfo("Firebase Auth persistence check complete")
        } catch (e: Exception) {
            logError("Error with Firebase Auth persistence: ${e.message}", e)
        }

        // Add auth state listener
        auth.addAuthStateListener { firebaseAuth ->
            val user = firebaseAuth.currentUser
            if (user != null) {
                logInfo("Auth state: SIGNED IN, UID: ${user.uid}${if (BuildConfig.DEBUG) ", Email: ${user.email}" else ""}")
                // if (user.isEmailVerified && user.email != null) { // Consider previouslyVerifiedEmail logic
                // previouslyVerifiedEmail = user.email
                // }
                appScope.launch {
                    ensureUserInFirestore(user)
                }
            } else {
                logInfo("Auth state: SIGNED OUT")
                // previouslyVerifiedEmail = null
            }
        }

        appScope.launch {
            refreshUserTokenSafely()
        }
        logDebug("Auth instance: $auth")
        logDebug("Current user: ${auth.currentUser?.uid}")
    }

    override fun onStateChanged(source: LifecycleOwner, event: Lifecycle.Event) {
        when (event) {
            Lifecycle.Event.ON_START -> {
                isAppInForeground = true
                logInfo("App transitioned to FOREGROUND")
            }
            Lifecycle.Event.ON_STOP -> {
                isAppInForeground = false
                logInfo("App transitioned to BACKGROUND")
            }
            else -> { /* Do nothing */ }
        }
    }

    private suspend fun ensureUserInFirestore(user: FirebaseUser) {
        try {
            val userDocRef = FirebaseFirestore.getInstance()
                .collection(USERS_COLLECTION)
                .document(user.uid)
            val userDoc = userDocRef.get().await()

            if (!userDoc.exists()) {
                logInfo("User ${user.uid} not in Firestore. Creating document.")
                val userData = hashMapOf(
                    "phoneNumber" to (user.phoneNumber ?: ""),
                    "email" to (user.email ?: ""), // Consider PII for release if not essential
                    "displayName" to (user.displayName ?: ""),
                    "photoUrl" to (user.photoUrl?.toString() ?: ""),
                    "createdAt" to com.google.firebase.firestore.FieldValue.serverTimestamp()
                )
                userDocRef.set(userData).await()
                logInfo("Created user document in Firestore for ${user.uid}")
            }
        } catch (e: Exception) {
            logError("Error ensuring user in Firestore: ${e.message}", e)
        }
    }

    private suspend fun refreshUserTokenSafely() {
        auth.currentUser?.let { user ->
            try {
                logDebug("Checking Firestore existence for user: ${user.uid} in Application")
                val userExists = FirebaseFirestore.getInstance()
                    .collection(USERS_COLLECTION)
                    .document(user.uid)
                    .get().await().exists()
                if (!userExists) {
                    ensureUserInFirestore(user)
                } else {
                    logDebug("User ${user.uid} confirmed to exist in Firestore.")
                }
            } catch (e: Exception) {
                logError("Error during Firestore check in refreshUserTokenSafely: ${e.message}", e)
            }
        }
    }

    override fun newImageLoader(): ImageLoader {
        return ImageLoader.Builder(this).apply {
            crossfade(true)
            memoryCachePolicy(CachePolicy.ENABLED)
            diskCachePolicy(CachePolicy.ENABLED)
            memoryCache {
                MemoryCache.Builder(this@CantDecideApplication)
                    .maxSizePercent(0.25)
                    .build()
            }
            diskCache {
                DiskCache.Builder()
                    .directory(<EMAIL>("image_cache"))
                    .maxSizePercent(0.05)
                    .build()
            }
            okHttpClient {
                OkHttpClient.Builder()
                    .connectTimeout(15, TimeUnit.SECONDS)
                    .readTimeout(15, TimeUnit.SECONDS)
                    .build()
            }
            respectCacheHeaders(false)
            if (BuildConfig.DEBUG) {
                logger(DebugLogger())
            }
        }.build()
    }

    // Helper logging functions
    private fun logDebug(message: String) {
        if (BuildConfig.DEBUG) Log.d(TAG, message)
    }

    private fun logInfo(message: String) {
        Log.i(TAG, message)
    }

    private fun logError(message: String, throwable: Throwable? = null) {
        Log.e(TAG, message, throwable)
    }
} 