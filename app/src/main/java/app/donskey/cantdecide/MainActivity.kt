package app.donskey.cantdecide

import android.content.Intent
import android.os.Bundle
import android.util.Log
import androidx.activity.ComponentActivity
import androidx.activity.compose.setContent
import androidx.appcompat.app.AppCompatDelegate
import androidx.compose.foundation.layout.Box
import androidx.compose.foundation.layout.fillMaxSize
import androidx.compose.foundation.layout.padding
import androidx.compose.material3.Icon
import androidx.compose.material3.MaterialTheme
import androidx.compose.material3.NavigationBar
import androidx.compose.material3.NavigationBarItem
import androidx.compose.material3.Scaffold
import androidx.compose.material3.SnackbarDuration
import androidx.compose.material3.SnackbarHost
import androidx.compose.material3.SnackbarHostState
import androidx.compose.material3.Surface
import androidx.compose.material3.Text
import androidx.compose.runtime.Composable
import androidx.compose.runtime.LaunchedEffect
import androidx.compose.runtime.getValue
import androidx.compose.runtime.mutableStateOf
import androidx.compose.runtime.remember
import androidx.compose.runtime.rememberCoroutineScope
import androidx.compose.runtime.setValue
import androidx.compose.ui.Modifier
import androidx.compose.ui.res.painterResource
import androidx.compose.ui.res.stringResource
import androidx.hilt.navigation.compose.hiltViewModel
import androidx.lifecycle.lifecycleScope
import androidx.navigation.NavDestination.Companion.hierarchy
import androidx.navigation.NavGraph.Companion.findStartDestination
import androidx.navigation.NavType
import androidx.navigation.compose.NavHost
import androidx.navigation.compose.composable
import androidx.navigation.compose.currentBackStackEntryAsState
import androidx.navigation.compose.rememberNavController
import androidx.navigation.navArgument
import app.donskey.cantdecide.data.repositories.UserRepository
import app.donskey.cantdecide.firebase.FirebaseHelper
import app.donskey.cantdecide.presentation.features.polls.create_private_text_poll.CreatePrivateTextPollScreen
import app.donskey.cantdecide.ui.friends.FriendsNavigation
import app.donskey.cantdecide.ui.friends.FriendsViewModel
import app.donskey.cantdecide.ui.poll_details.PollDetailsScreen
import app.donskey.cantdecide.ui.polls.CreatePollNavigation
import app.donskey.cantdecide.ui.polls.CreatePrivatePollScreen
import app.donskey.cantdecide.ui.polls.CreatePublicPollOptionsScreen
import app.donskey.cantdecide.ui.polls.PollsDestinations
import app.donskey.cantdecide.ui.polls.PollsScreen
import app.donskey.cantdecide.ui.polls.PrivateImagePollScreen
import app.donskey.cantdecide.ui.polls.PrivatePollsHistoryScreen
import app.donskey.cantdecide.ui.polls.PrivateVideoPollScreen
import app.donskey.cantdecide.ui.polls.PublicPollsHistoryScreen
import app.donskey.cantdecide.ui.polls.ViewPublicPollsScreen
import app.donskey.cantdecide.ui.settings.SettingsScreenContainer
import app.donskey.cantdecide.ui.theme.AppTheme
import app.donskey.cantdecide.ui.theme.ThemeManager
import app.donskey.cantdecide.ui.view_private_polls.ViewPrivatePollsScreen
import app.donskey.cantdecide.util.SnackbarMessageManager
import com.google.firebase.auth.FirebaseAuth
import dagger.hilt.android.AndroidEntryPoint
import kotlinx.coroutines.flow.collectLatest
import kotlinx.coroutines.launch
import javax.inject.Inject

/**
 * Main activity that hosts the bottom navigation and manages the main navigation flow using Jetpack Compose.
 */
@AndroidEntryPoint
class MainActivity : ComponentActivity() {
    @Inject lateinit var userRepository: UserRepository
    @Inject lateinit var auth: FirebaseAuth
    private val initialNavigationTarget = mutableStateOf<String?>(null)

    companion object {
        private const val TAG = "MainActivity"
    }

    override fun onCreate(savedInstanceState: Bundle?) {
        super.onCreate(savedInstanceState)
        Log.d(TAG, "onCreate: Started with intent: $intent")

        // Initialize theme preferences from user settings
        initializeTheme()

        // Log main activity opened event
        FirebaseHelper.logEvent("main_activity_opened")

        // Handle intent extras for initial navigation
        handleIntentExtras(intent)

        setContent {
            // Provide the initial navigation target to the main screen
            AppTheme {
                MainScreen(
                    initialTargetRoute = initialNavigationTarget.value,
                    onInitialTargetConsumed = {
                        initialNavigationTarget.value = null
                    }
                )
            }
        }
    }

    // Handle new intents if the activity is already running (e.g., singleTop)
    override fun onNewIntent(intent: Intent) {
        super.onNewIntent(intent)
        Log.d(TAG, "onNewIntent: Received new intent: $intent")
        // It's important to update the activity's intent to the new one.
        setIntent(intent)
        // For now, relying on MainScreen's LaunchedEffect(initialTargetRoute) which reads from mutableStateOf.
        handleIntentExtras(intent)
    }

    private fun handleIntentExtras(intent: Intent?) {
        if (intent?.hasExtra("navigateTo") == true) {
            val targetRoute = intent.getStringExtra("navigateTo")
            Log.d(TAG, "handleIntentExtras: Setting initialNavigationTarget to $targetRoute")
            initialNavigationTarget.value = targetRoute

            // If navigation needs to happen immediately based on intent, and before MainScreen recomposes,
            // this is where it gets complex without a shared NavController or ViewModel event.
            // The current design relies on MainScreen recomposing due to initialNavigationTarget change.
        }
    }

    /**
     * Initializes theme settings by fetching user preferences from the repository.
     */
    private fun initializeTheme() {
        // Fetch user theme preferences in a coroutine
        lifecycleScope.launch {
            try {
                val userProfile = userRepository.getUserProfile()
                userProfile?.let { user ->
                    // Initialize ThemeManager with user preferences
                    ThemeManager.initializeFromPreferences(
                        isDark = user.themePreferences.darkMode,
                        colorIndex = user.themePreferences.themeColorIndex
                    )

                    // Apply the theme to the app
                    applyTheme(user.themePreferences.darkMode)
                }
            } catch (e: Exception) {
                // If there's an error, use default theme settings
                ThemeManager.initializeFromPreferences(isDark = false, colorIndex = 0)

                // Apply default theme
                applyTheme(false)
            }
        }
    }

    /**
     * Applies the theme based on dark mode setting.
     */
    private fun applyTheme(isDark: Boolean) {
        val mode = if (isDark) {
            AppCompatDelegate.MODE_NIGHT_YES
        } else {
            AppCompatDelegate.MODE_NIGHT_NO
        }

        // Apply the night mode setting
        AppCompatDelegate.setDefaultNightMode(mode)
    }
}

/**
 * Sealed class representing the navigation items in the bottom navigation bar.
 */
sealed class BottomNavItem(
    val route: String,
    val titleResId: Int,
    val iconResId: Int
) {
    data object Polls : BottomNavItem("polls", R.string.title_polls, R.drawable.ic_polls_black_24dp)
    data object CreatePoll : BottomNavItem("create_poll", R.string.title_create_poll, R.drawable.ic_create_poll_black_24dp)
    data object Friends : BottomNavItem("friends", R.string.title_friends, R.drawable.ic_friends_black_24dp)
    data object Settings : BottomNavItem("settings", R.string.title_settings, R.drawable.ic_settings_black_24dp)
}

// Route definition for Poll Details
const val POLL_DETAILS_ROUTE_BASE = "pollDetails"
const val POLL_DETAILS_ARG_POLL_ID = "pollId"
const val POLL_DETAILS_ROUTE = "$POLL_DETAILS_ROUTE_BASE/{$POLL_DETAILS_ARG_POLL_ID}"

/**
 * Main screen with bottom navigation and content area.
 */
@Composable
fun MainScreen(
    initialTargetRoute: String?,
    onInitialTargetConsumed: () -> Unit
) {
    val navController = rememberNavController()
    val snackbarHostState = remember { SnackbarHostState() }
    val scope = rememberCoroutineScope()

    LaunchedEffect(initialTargetRoute, navController) {
        if (initialTargetRoute != null) {
            Log.d("MainScreen", "LaunchedEffect: Navigating to initialTargetRoute: $initialTargetRoute")
            try {
                navController.navigate(initialTargetRoute) {
                    popUpTo(navController.graph.findStartDestination().id) {
                        saveState = true
                    }
                    launchSingleTop = true
                    restoreState = true
                }
            } catch (e: IllegalArgumentException) {
                Log.e("MainScreen", "Navigation error: Could not navigate to route '$initialTargetRoute'. ${e.message}")
            }
            onInitialTargetConsumed()
        }
    }

    LaunchedEffect(Unit) {
        SnackbarMessageManager.messages.collectLatest { snackbarMessage ->
            scope.launch {
                snackbarHostState.showSnackbar(
                    message = snackbarMessage.message,
                    duration = SnackbarDuration.Long
                )
            }
        }
    }

    val navItems = listOf(
        BottomNavItem.Polls,
        BottomNavItem.CreatePoll,
        BottomNavItem.Friends,
        BottomNavItem.Settings
    )

    val navBackStackEntry by navController.currentBackStackEntryAsState()
    val currentDestination = navBackStackEntry?.destination

    LaunchedEffect(currentDestination?.route) {
        currentDestination?.route?.let {
            FirebaseHelper.logEvent("screen_view", Bundle().apply {
                putString("screen_name", it)
            })
        }
    }

    var resetFriendsNav by remember { mutableStateOf(false) }
    var resetSettingsNav by remember { mutableStateOf(false) }
    var resetCreatePollNav by remember { mutableStateOf(false) }

    val pollsScreenChildrenRoutes = remember {
        setOf(
            PollsDestinations.CREATE_PRIVATE_POLL_OPTIONS,
            PollsDestinations.VIEW_PRIVATE_TEXT_POLLS,
            PollsDestinations.PRIVATE_POLLS_HISTORY,
            PollsDestinations.CREATE_PUBLIC_POLL_OPTIONS,
            PollsDestinations.VIEW_PUBLIC_POLLS,
            PollsDestinations.PUBLIC_POLLS_HISTORY,
            "private_text_poll",
            "private_image_poll",
            "private_video_poll",
            POLL_DETAILS_ROUTE
        )
    }

    Scaffold(
        snackbarHost = { SnackbarHost(hostState = snackbarHostState) },
        bottomBar = {
            NavigationBar(
                containerColor = MaterialTheme.colorScheme.primary,
            ) {
                val currentNavBackStackEntry by navController.currentBackStackEntryAsState()
                val currentNavDestination = currentNavBackStackEntry?.destination

                navItems.forEach { item ->
                    val isSelected = currentNavDestination?.hierarchy?.any { it.route == item.route } == true

                    NavigationBarItem(
                        icon = {
                            Icon(
                                painter = painterResource(id = item.iconResId),
                                contentDescription = stringResource(id = item.titleResId)
                            )
                        },
                        label = { Text(stringResource(id = item.titleResId)) },
                        selected = isSelected,
                        onClick = {
                            val currentRoute = navController.currentDestination?.route
                            Log.d("MainActivityOnClick", "-----------------------------------------------------")
                            Log.d("MainActivityOnClick", "Item Clicked: ${item.route}, Current Route: $currentRoute, IsSelected (via hierarchy): $isSelected")

                            if (item.route == BottomNavItem.Polls.route && pollsScreenChildrenRoutes.contains(currentRoute)) {
                                Log.d("MainActivityOnClick", "POLLS TAB re-selected on a child ($currentRoute). Popping back to ${BottomNavItem.Polls.route}.")
                                navController.popBackStack(BottomNavItem.Polls.route, inclusive = false)
                            } else {
                                val isReselectingCurrentTabRoot = currentRoute == item.route
                                if (isSelected && !isReselectingCurrentTabRoot) {
                                    when (item.route) {
                                        BottomNavItem.Friends.route -> {
                                            Log.d("MainActivityOnClick", "FRIENDS TAB re-selected on child. Setting resetFriendsNav.")
                                            resetFriendsNav = true
                                        }
                                        BottomNavItem.Settings.route -> {
                                            Log.d("MainActivityOnClick", "SETTINGS TAB re-selected on child. Setting resetSettingsNav.")
                                            resetSettingsNav = true
                                        }
                                        BottomNavItem.CreatePoll.route -> {
                                            Log.d("MainActivityOnClick", "CREATE_POLL TAB re-selected on child. Setting resetCreatePollNav.")
                                            resetCreatePollNav = true
                                        }
                                    }
                                } else if (isReselectingCurrentTabRoot) {
                                    Log.d("MainActivityOnClick", "Re-selected root of tab ${item.route}.")
                                    when (item.route) {
                                        BottomNavItem.Friends.route -> { Log.d("MainActivityOnClick", "FRIENDS TAB reset on root."); resetFriendsNav = true }
                                        BottomNavItem.Settings.route -> { Log.d("MainActivityOnClick", "SETTINGS TAB reset on root."); resetSettingsNav = true }
                                        BottomNavItem.CreatePoll.route -> { Log.d("MainActivityOnClick", "CREATE_POLL TAB reset on root."); resetCreatePollNav = true }
                                    }
                                }
                            }

                            Log.d("MainActivityOnClick", "Executing general navigate to ${item.route}.")
                            navController.navigate(item.route) {
                                popUpTo(navController.graph.findStartDestination().id) {
                                    saveState = true
                                }
                                launchSingleTop = true
                                restoreState = true
                            }
                            Log.d("MainActivityOnClick", "Current destination after all: ${navController.currentDestination?.route}")
                        }
                    )
                }
            }
        }
    ) { innerPadding ->
        Surface(
            modifier = Modifier
                .fillMaxSize()
                .padding(innerPadding),
            color = MaterialTheme.colorScheme.background
        ) {
            NavHost(
                navController = navController,
                startDestination = BottomNavItem.Polls.route
            ) {
                composable(BottomNavItem.Polls.route) {
                    PollsScreen(navController = navController)
                }
                composable(BottomNavItem.CreatePoll.route) {
                    Box(modifier = Modifier.fillMaxSize()) {
                        val createPollNavController = rememberNavController()
                        LaunchedEffect(resetCreatePollNav) {
                            if (resetCreatePollNav) {
                                try {
                                    createPollNavController.navigate("create_poll_main") {
                                        popUpTo("create_poll_main") { inclusive = false }
                                    }
                                } catch (e: Exception) {
                                    Log.d("MainActivity", "Failed to navigate to create_poll_main: ${e.message}")
                                }
                                resetCreatePollNav = false
                            }
                        }
                        CreatePollNavigation(navController = createPollNavController, modifier = Modifier.fillMaxSize())
                    }
                }
                composable(BottomNavItem.Friends.route) {
                    Box(modifier = Modifier.fillMaxSize()) {
                        val friendsNavController = rememberNavController()
                        val friendsViewModel: FriendsViewModel = hiltViewModel()

                        LaunchedEffect(resetFriendsNav) {
                            if (resetFriendsNav) {
                                try {
                                    friendsNavController.navigate("friends_list") {
                                        popUpTo("friends_list") { inclusive = false }
                                    }
                                } catch (e: Exception) {
                                    Log.d("MainActivity", "Failed to navigate to friends_list: ${e.message}")
                                }
                                resetFriendsNav = false
                            }
                        }
                        FriendsNavigation(navController = friendsNavController, friendsViewModel = friendsViewModel, modifier = Modifier.fillMaxSize())
                    }
                }
                composable(BottomNavItem.Settings.route) {
                    Box(modifier = Modifier.fillMaxSize()) {
                        val settingsNavController = rememberNavController()
                        LaunchedEffect(resetSettingsNav) {
                            if (resetSettingsNav) {
                                try {
                                    settingsNavController.navigate("settings_view") {
                                        popUpTo("settings_view") { inclusive = false }
                                    }
                                } catch (e: Exception) {
                                    Log.d("MainActivity", "Failed to navigate to settings_view: ${e.message}")
                                }
                                resetSettingsNav = false
                            }
                        }
                        SettingsScreenContainer(navController = settingsNavController)
                    }
                }

                // Routes for PollsHubScreen (PollsScreen.kt)
                composable(PollsDestinations.CREATE_PRIVATE_POLL_OPTIONS) {
                    CreatePrivatePollScreen(navController = navController)
                }
                composable(PollsDestinations.VIEW_PRIVATE_TEXT_POLLS) {
                    ViewPrivatePollsScreen(
                        navController = navController,
                        onNavigateBack = { navController.popBackStack() }
                    )
                }
                composable(PollsDestinations.PRIVATE_POLLS_HISTORY) {
                    PrivatePollsHistoryScreen(navController = navController)
                }
                composable(PollsDestinations.CREATE_PUBLIC_POLL_OPTIONS) {
                    CreatePublicPollOptionsScreen(navController = navController)
                }
                composable(PollsDestinations.VIEW_PUBLIC_POLLS) {
                    ViewPublicPollsScreen(navController = navController)
                }
                composable(PollsDestinations.PUBLIC_POLLS_HISTORY) {
                    PublicPollsHistoryScreen(navController = navController)
                }

                composable("private_text_poll") {
                    CreatePrivateTextPollScreen(navController = navController)
                }
                composable("private_image_poll") {
                    PrivateImagePollScreen(navController = navController)
                }
                composable("private_video_poll") {
                    PrivateVideoPollScreen(navController = navController)
                }

                composable(
                    route = POLL_DETAILS_ROUTE,
                    arguments = listOf(navArgument(POLL_DETAILS_ARG_POLL_ID) { type = NavType.StringType })
                ) {
                    PollDetailsScreen(
                        onNavigateBack = { navController.popBackStack() }
                    )
                }
            }
        }
    }
}