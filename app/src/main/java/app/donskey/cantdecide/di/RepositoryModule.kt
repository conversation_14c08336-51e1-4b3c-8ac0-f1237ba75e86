package app.donskey.cantdecide.di

import app.donskey.cantdecide.data.repository.PollsRepositoryImpl
import app.donskey.cantdecide.domain.repository.PollsRepository
import app.donskey.cantdecide.data.repositories.FriendsRepository
import app.donskey.cantdecide.domain.repository.UserRepository
import app.donskey.cantdecide.data.repository.UserRepositoryImpl
import com.google.firebase.auth.FirebaseAuth
import com.google.firebase.firestore.FirebaseFirestore
import dagger.Module
import dagger.Provides
import dagger.hilt.InstallIn
import dagger.hilt.components.SingletonComponent
import javax.inject.Singleton

/**
 * Hilt module for providing repository implementations.
 */
@Module
@InstallIn(SingletonComponent::class) // Repositories are typically scoped to the application lifecycle
object RepositoryModule {

    /**
     * Provides [PollsRepository] implementation.
     *
     * @param firestore The Firebase Firestore instance.
     * @return An instance of [PollsRepository].
     */
    @Provides
    @Singleton
    fun providePollsRepository(firestore: FirebaseFirestore): PollsRepository {
        // Assuming PollsRepositoryImpl is the concrete implementation
        return PollsRepositoryImpl(firestore)
    }

    @Provides
    @Singleton
    fun provideFriendsRepository(auth: FirebaseAuth, firestore: FirebaseFirestore): FriendsRepository {
        // Directly provides the concrete FriendsRepository class
        return FriendsRepository(auth, firestore)
    }
    
    /**
     * Provides [UserRepository] implementation.
     *
     * @param firebaseAuth The Firebase Auth instance.
     * @return An instance of [UserRepositoryImpl] bound to [UserRepository].
     */
    @Provides
    @Singleton
    fun provideUserRepository(firebaseAuth: FirebaseAuth): UserRepository {
        return UserRepositoryImpl(firebaseAuth)
    }
} 