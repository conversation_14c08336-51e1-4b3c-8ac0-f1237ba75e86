package app.donskey.cantdecide.di

import com.google.firebase.auth.FirebaseAuth
import com.google.firebase.firestore.FirebaseFirestore
import com.google.firebase.ktx.Firebase
import com.google.firebase.firestore.ktx.firestore
import dagger.Module
import dagger.Provides
import dagger.hilt.InstallIn
import dagger.hilt.components.SingletonComponent
import javax.inject.Singleton

/**
 * Hilt module for providing Firebase service instances.
 */
@Module
@InstallIn(SingletonComponent::class) // Firebase services are typically singletons for the app lifecycle
object FirebaseModule {

    /**
     * Provides an instance of [FirebaseAuth].
     * @return [FirebaseAuth] instance.
     */
    @Provides
    @Singleton
    fun provideFirebaseAuth(): FirebaseAuth {
        return FirebaseAuth.getInstance()
    }

    /**
     * Provides an instance of [FirebaseFirestore].
     * @return [FirebaseFirestore] instance.
     */
    @Provides
    @Singleton
    fun provideFirebaseFirestore(): FirebaseFirestore {
        return Firebase.firestore
    }
    
    // Add other Firebase services here if needed (e.g., FirebaseStorage)
} 