package app.donskey.cantdecide.util

import kotlinx.coroutines.flow.MutableSharedFlow
import kotlinx.coroutines.flow.asSharedFlow

/**
 * A simple data class to represent a message to be shown in a Snackbar.
 *
 * @property message The text content of the Snackbar.
 * @property duration The duration for which the Snackbar should be shown.
 */
data class SnackbarMessage(
    val message: String,
    // We can add other properties like actionLabel, duration, etc. later if needed.
    // For now, focusing on the message as per the request.
    // The user requested a duration of 5 seconds, which maps to SnackbarDuration.Long.
    // We'll handle the duration in the UI layer when showing the Snackbar.
)

/**
 * <PERSON>ton object to manage and broadcast Snackbar messages across the application.
 * This allows services or other background components to request a Snackbar to be shown
 * by the main UI layer.
 */
object SnackbarMessageManager {

    // A private MutableSharedFlow that will be used to emit SnackbarMessage events.
    // replay = 0 ensures that new subscribers don't get old messages.
    // extraBufferCapacity = 1 allows one message to be buffered if the collector is slow,
    // preventing suspension of the sender in most common cases.
    private val _messages = MutableSharedFlow<SnackbarMessage>(replay = 0, extraBufferCapacity = 1)

    /**
     * A public SharedFlow that UI components can collect to receive Snackbar messages.
     */
    val messages = _messages.asSharedFlow()

    /**
     * Function to post a new message to be displayed as a Snackbar.
     * This can be called from any part of the application, including background services.
     *
     * @param message The text of the message to display.
     */
    fun showMessage(message: String) {
        // Try to emit the message. If the buffer is full (should be rare with extraBufferCapacity),
        // it will suspend until space is available or fail if the scope is cancelled.
        // For a simple manager like this, tryEmit is often sufficient if we don't want to suspend the caller.
        // If tryEmit fails, it means the UI is not collecting or is too slow, and the message might be dropped.
        // Given the context of notifications, dropping a Snackbar message is less critical than crashing.
        val result = _messages.tryEmit(SnackbarMessage(message))
        if (!result) {
            // Optional: Log if the message couldn't be emitted, e.g.,
            // Log.w("SnackbarMessageManager", "Failed to emit snackbar message, UI not collecting?")
        }
    }
} 