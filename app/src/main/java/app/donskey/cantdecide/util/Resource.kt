package app.donskey.cantdecide.util

// A generic class that holds a value with its loading status.
// @param T the type of data being handled.
sealed class Resource<T>(
    val data: T? = null,
    val message: String? = null
) {
    // Represents a successful state with data.
    // @param data The data of type T. Cannot be null.
    class Success<T>(data: T) : Resource<T>(data)

    // Represents an error state with an optional message and data.
    // @param message An optional error message.
    // @param data Optional data that might be relevant to the error (e.g., cached data).
    class Error<T>(message: String, data: T? = null) : Resource<T>(data, message)

    // Represents a loading state, optionally with partial data.
    // @param data Optional partial data available during loading.
    class Loading<T>(data: T? = null) : Resource<T>(data)
} 