package app.donskey.cantdecide.util

import kotlinx.coroutines.CoroutineScope
import kotlinx.coroutines.Dispatchers
import kotlinx.coroutines.SupervisorJob
import kotlinx.coroutines.cancel

/**
 * Provides a singleton CoroutineScope that survives longer than individual service instances,
 * suitable for launching background tasks like fetching data and showing notifications from FCM messages.
 * Use Dispatchers.IO for potentially blocking operations like database/network access.
 */
object NotificationScope {
    private val supervisorJob = SupervisorJob()
    val scope = CoroutineScope(Dispatchers.IO + supervisorJob)

    /**
     * Call this from your Application's `onTerminate` if needed, although SupervisorJob
     * generally doesn't need explicit cancellation unless the whole app process is ending
     * in a controlled way (which is rare on Android).
     */
    fun cancelScope() {
        supervisorJob.cancel("Application scope cancelled.")
    }
} 