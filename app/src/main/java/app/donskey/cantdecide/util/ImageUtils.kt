package app.donskey.cantdecide.util

import android.content.ContentResolver
import android.content.Context
import android.net.Uri
import android.util.Log
import java.io.File
import java.io.FileOutputStream
import java.util.UUID

/**
 * Utility class for handling images in the application.
 */
object ImageUtils {
    private const val TAG = "ImageUtils"

    /**
     * Copies an image from a content URI to the app's internal storage.
     * This ensures the image is accessible even after app restarts.
     *
     * @param context Context The application context
     * @param sourceUri Uri The content URI of the source image
     * @return Uri? The file URI of the copied image or null if copying failed
     */
    fun copyImageToInternalStorage(context: Context, sourceUri: Uri): Uri? {
        val contentResolver: ContentResolver = context.contentResolver

        try {
            // Create a unique filename
            val fileName = "profile_${UUID.randomUUID()}.jpg"

            // Create a file in internal storage
            val directory = File(context.filesDir, "profile_pictures")
            if (!directory.exists()) {
                directory.mkdirs()
            }

            val destFile = File(directory, fileName)

            // Copy the file
            contentResolver.openInputStream(sourceUri)?.use { input ->
                FileOutputStream(destFile).use { output ->
                    input.copyTo(output)
                }
            } ?: return null

            // Return a URI to the copied file
            Log.d(TAG, "Image copied successfully to: ${destFile.absolutePath}")
            return Uri.fromFile(destFile)

        } catch (e: Exception) {
            Log.e(TAG, "Error copying image: ${e.message}", e)
            return null
        }
    }
} 