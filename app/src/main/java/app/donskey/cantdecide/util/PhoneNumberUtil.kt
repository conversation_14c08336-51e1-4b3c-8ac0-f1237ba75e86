package app.donskey.cantdecide.util

/**
 * Utility class for phone number validation and formatting.
 */
object PhoneNumberUtil {
    
    /**
     * Validates if a phone number is in a valid format.
     * 
     * @param phoneNumber String The phone number to validate
     * @return Boolean True if the phone number is valid, false otherwise
     */
    fun isValidPhoneNumber(phoneNumber: String): Boolean {
        // Basic validation: at least 8 digits
        val digitsOnly = phoneNumber.replace(Regex("[^0-9]"), "")
        return digitsOnly.length >= 8
    }
    
    /**
     * Formats a phone number with international code for E.164 format.
     * 
     * @param phoneNumber String The phone number to format
     * @param countryCode String The country code (e.g., "+61")
     * @return String The formatted phone number
     */
    fun formatPhoneNumberE164(phoneNumber: String, countryCode: String): String {
        val digitsOnly = phoneNumber.replace(Regex("[^0-9]"), "")
        val normalizedCountryCode = if (countryCode.startsWith("+")) countryCode else "+$countryCode"
        
        return "$normalizedCountryCode$digitsOnly"
    }
    
    /**
     * Formats a phone number in a user-friendly display format.
     * 
     * @param phoneNumber String The phone number to format
     * @param countryCode String The country code (e.g., "+61")
     * @return String The formatted phone number for display
     */
    fun formatPhoneNumberForDisplay(phoneNumber: String, countryCode: String): String {
        val digitsOnly = phoneNumber.replace(Regex("[^0-9]"), "")
        val normalizedCountryCode = if (countryCode.startsWith("+")) countryCode else "+$countryCode"
        
        // A simple display format with spaces
        return "$normalizedCountryCode ${digitsOnly.chunked(3).joinToString(" ")}"
    }
} 