package app.donskey.cantdecide.ui.friends


import android.util.Log
import android.widget.Toast
import androidx.activity.compose.rememberLauncherForActivityResult
import androidx.activity.result.contract.ActivityResultContracts
import androidx.compose.foundation.clickable
import androidx.compose.foundation.layout.Box
import androidx.compose.foundation.layout.Column
import androidx.compose.foundation.layout.Row
import androidx.compose.foundation.layout.Spacer
import androidx.compose.foundation.layout.fillMaxSize
import androidx.compose.foundation.layout.fillMaxWidth
import androidx.compose.foundation.layout.height
import androidx.compose.foundation.layout.padding
import androidx.compose.foundation.layout.width

import androidx.compose.foundation.shape.RoundedCornerShape

import androidx.compose.material.icons.Icons
import androidx.compose.material.icons.automirrored.filled.ArrowBack
import androidx.compose.material.icons.automirrored.filled.ArrowForward
import androidx.compose.material.icons.filled.AccountCircle

import androidx.compose.material3.Card
import androidx.compose.material3.CardDefaults
import androidx.compose.material3.CircularProgressIndicator
import androidx.compose.material3.ExperimentalMaterial3Api
import androidx.compose.material3.Icon
import androidx.compose.material3.MaterialTheme
import androidx.compose.material3.Scaffold
import androidx.compose.material3.Surface
import androidx.compose.material3.Text
import androidx.compose.material3.TopAppBar
import androidx.compose.material3.TopAppBarDefaults
import androidx.compose.runtime.Composable
import androidx.compose.runtime.LaunchedEffect
import androidx.compose.runtime.getValue
import androidx.compose.runtime.mutableStateOf
import androidx.compose.runtime.remember

import androidx.compose.runtime.setValue
import androidx.compose.ui.Alignment
import androidx.compose.ui.Modifier
import androidx.compose.ui.platform.LocalContext
import androidx.compose.ui.res.painterResource
import androidx.compose.ui.res.stringResource
import androidx.compose.ui.text.font.FontWeight
import androidx.compose.ui.text.style.TextAlign
import androidx.compose.ui.tooling.preview.Preview
import androidx.compose.ui.unit.dp

import androidx.lifecycle.viewmodel.compose.viewModel
import androidx.navigation.NavController
import androidx.navigation.compose.rememberNavController
import app.donskey.cantdecide.R
import androidx.compose.foundation.lazy.LazyColumn
import androidx.compose.foundation.lazy.items
import androidx.compose.runtime.collectAsState
import androidx.compose.material3.AlertDialog
import androidx.compose.material3.TextButton
import app.donskey.cantdecide.ui.theme.AppTheme
import app.donskey.cantdecide.util.SnackbarMessageManager

/**
 * Screen for inviting friends, with options to search contacts or manually enter a phone number.
 */
@OptIn(ExperimentalMaterial3Api::class)
@Composable
fun InviteFriendScreen(
    navController: NavController,
    viewModel: FriendsViewModel
) {
    val context = LocalContext.current
    val uiState by viewModel.uiState.collectAsState()
    
    // State for controlling the send request confirmation dialog
    var showSendRequestDialog by remember { mutableStateOf(false) }
    var contactToSendRequestTo by remember { mutableStateOf<DeviceContact?>(null) }

    // State for the invite link dialog
    var showInviteLinkDialog by remember { mutableStateOf(false) }
    var inviteLinkDialogMessage by remember { mutableStateOf<String?>(null) }
    
    LaunchedEffect(uiState.friendRequestStatusMessage) {
        uiState.friendRequestStatusMessage?.let { message ->
            // Check if this is the specific message for sharing an invite link (for the dialog)
            if (message.contains("to install the app. You can share this link:")) {
                inviteLinkDialogMessage = message
                showInviteLinkDialog = true
                // ViewModel message will be cleared when dialog is dismissed by the dialog's logic.
            } else {
                // For other messages (like "Friend request sent..."), show a Snackbar
                Log.d("InviteFriendScreen", "Showing Snackbar for message: $message")
                SnackbarMessageManager.showMessage(message)
                // Ensure message is cleared from ViewModel state after showing Snackbar to prevent re-triggering
                viewModel.clearFriendRequestStatusMessage() 
            }
        }
    }
    
    val contactsPermissionLauncher = rememberLauncherForActivityResult(
        ActivityResultContracts.RequestPermission()
    ) { isGranted ->
        if (isGranted) {
            Log.d("InviteFriendScreen", "Contacts permission granted by user.")
            viewModel.loadDeviceContacts(context)
        } else {
            Log.w("InviteFriendScreen", "Contacts permission denied by user.")
            Toast.makeText(
                context,
                context.getString(R.string.error_contacts_permission),
                Toast.LENGTH_LONG
            ).show()
        }
    }
    
    Scaffold(
        topBar = {
            TopAppBar(
                title = { Text(text = stringResource(R.string.invite_a_friend)) },
                navigationIcon = {
                    Icon(
                        imageVector = Icons.AutoMirrored.Filled.ArrowBack,
                        contentDescription = "Back",
                        modifier = Modifier
                            .padding(16.dp)
                            .clickable { navController.navigateUp() }
                    )
                },
                colors = TopAppBarDefaults.topAppBarColors(
                    containerColor = MaterialTheme.colorScheme.primary,
                    titleContentColor = MaterialTheme.colorScheme.onPrimary,
                    navigationIconContentColor = MaterialTheme.colorScheme.onPrimary
                )
            )
        }
    ) { padding ->
        Surface(
            modifier = Modifier
                .fillMaxSize()
                .padding(padding),
            color = MaterialTheme.colorScheme.background
        ) {
            Column(
                modifier = Modifier
                    .fillMaxSize()
                    .padding(16.dp)
            ) {
                Card(
                    modifier = Modifier
                        .fillMaxWidth()
                        .clickable { viewModel.requestContactsPermissionAndLoad(contactsPermissionLauncher, context) },
                    elevation = CardDefaults.cardElevation(
                        defaultElevation = 4.dp
                    ),
                    shape = RoundedCornerShape(8.dp),
                    colors = CardDefaults.cardColors(containerColor = MaterialTheme.colorScheme.surface)
                ) {
                    Row(
                        modifier = Modifier
                            .fillMaxWidth()
                            .padding(16.dp),
                        verticalAlignment = Alignment.CenterVertically
                    ) {
                        Icon(
                            painter = painterResource(id = R.drawable.ic_contacts_24dp),
                            contentDescription = stringResource(R.string.search_contacts),
                            tint = MaterialTheme.colorScheme.primary
                        )
                        
                        Spacer(modifier = Modifier.width(16.dp))
                        
                        Text(
                            text = stringResource(R.string.search_contacts),
                            style = MaterialTheme.typography.bodyLarge,
                            color = MaterialTheme.colorScheme.primary,
                            modifier = Modifier.weight(1f)
                        )
                        
                        Icon(
                            imageVector = Icons.AutoMirrored.Filled.ArrowForward,
                            contentDescription = stringResource(R.string.navigate_to),
                            tint = MaterialTheme.colorScheme.primary
                        )
                    }
                }
                
                // --- Results Section --- 
                // Only display this section if a search has been attempted
                if (uiState.hasSearchedContacts) {
                    Spacer(modifier = Modifier.height(16.dp))

                    Text(
                        text = stringResource(R.string.results, uiState.deviceContacts.size),
                        style = MaterialTheme.typography.headlineSmall,
                        fontWeight = FontWeight.Bold
                    )
    
                    Spacer(modifier = Modifier.height(8.dp))
    
                    // Loading indicator for contacts
                    if (uiState.isLoadingContacts) {
                        Box(modifier = Modifier.fillMaxWidth(), contentAlignment = Alignment.Center) {
                            CircularProgressIndicator()
                        }
                    } else {
                       // Assign error to local variable to fix smart cast issue
                       val currentError = uiState.contactsError 
                       if (!currentError.isNullOrBlank()) {
                           // Display contacts error message from ViewModel state
                           Text(
                               text = currentError,
                               color = MaterialTheme.colorScheme.error,
                               textAlign = TextAlign.Center,
                               modifier = Modifier.fillMaxWidth()
                           )
                       } else if (uiState.deviceContacts.isEmpty()) {
                            // Display "No results found" if list is empty and no error
                            Text(
                               text = stringResource(R.string.no_results), 
                               textAlign = TextAlign.Center,
                               modifier = Modifier.fillMaxWidth()
                            )
                       } else {
                           // Display the list of contacts using LazyColumn
                           LazyColumn(modifier = Modifier.weight(1f)) { // Use weight to fill remaining space
                               items(uiState.deviceContacts, key = { it.id }) { contact -> // Use contact ID as key
                                    ContactItem(contact = contact) { 
                                       // Show confirmation dialog instead of sending directly
                                       contactToSendRequestTo = contact
                                       showSendRequestDialog = true
                                       // Log.d("InviteFriendScreen", "Selected contact: ${contact.name} - ${contact.phoneNumber}")
                                       // viewModel.handleContactSelection(contact)
                                   }
                               }
                           }
                       }
                    }
                } // End of if (uiState.hasSearchedContacts)
            }
        }
    }

    // --- Send Request Confirmation Dialog ---
    if (showSendRequestDialog) {
        val contact = contactToSendRequestTo // Local val for smart cast
        if (contact != null) {
            AlertDialog(
                onDismissRequest = { 
                    showSendRequestDialog = false 
                    contactToSendRequestTo = null
                },
                title = { Text(text = stringResource(R.string.send_request_title)) },
                text = {
                    Text(
                        text = stringResource(
                            R.string.send_request_confirmation,
                            contact.name ?: stringResource(R.string.unknown_user),
                            contact.phoneNumber ?: ""
                        )
                    )
                },
                confirmButton = {
                    TextButton(
                        onClick = {
                            viewModel.handleContactSelection(contact)
                            showSendRequestDialog = false
                            contactToSendRequestTo = null
                        }
                    ) {
                        Text(stringResource(R.string.confirm))
                    }
                },
                dismissButton = {
                    TextButton(
                        onClick = {
                            showSendRequestDialog = false
                            contactToSendRequestTo = null
                        }
                    ) {
                        Text(stringResource(R.string.cancel))
                    }
                }
            )
        }
    }

    // --- Invite Link Dialog ---
    if (showInviteLinkDialog) {
        inviteLinkDialogMessage?.let { message ->
            AlertDialog(
                onDismissRequest = {
                    showInviteLinkDialog = false
                    inviteLinkDialogMessage = null
                    viewModel.clearFriendRequestStatusMessage() // Clear message from VM
                },
                title = { Text(text = stringResource(R.string.invite_friend_title)) }, // Consider adding a new string resource
                text = { Text(text = message) },
                confirmButton = {
                    TextButton(
                        onClick = {
                            showInviteLinkDialog = false
                            inviteLinkDialogMessage = null
                            viewModel.clearFriendRequestStatusMessage() // Clear message from VM
                        }
                    ) {
                        Text(stringResource(android.R.string.ok))
                    }
                }
            )
        }
    }
}

@Composable
fun ContactItem(contact: DeviceContact, onClick: () -> Unit) {
    Card(
        modifier = Modifier
            .fillMaxWidth()
            .padding(vertical = 4.dp)
            .clickable(onClick = onClick),
        elevation = CardDefaults.cardElevation(defaultElevation = 2.dp),
        shape = RoundedCornerShape(8.dp),
        colors = CardDefaults.cardColors(containerColor = MaterialTheme.colorScheme.surface)
    ) {
        Row(
            modifier = Modifier
                .fillMaxWidth()
                .padding(12.dp),
            verticalAlignment = Alignment.CenterVertically
        ) {
            Icon(
                imageVector = Icons.Default.AccountCircle,
                contentDescription = "Contact Icon",
                tint = MaterialTheme.colorScheme.primary,
                modifier = Modifier.padding(end = 12.dp)
            )
            Column(modifier = Modifier.weight(1f)) {
                Text(
                    text = contact.name ?: "Unknown Name",
                    style = MaterialTheme.typography.bodyLarge,
                    fontWeight = FontWeight.Medium,
                    color = MaterialTheme.colorScheme.primary
                )
                Text(
                    text = contact.phoneNumber ?: "No phone number",
                    style = MaterialTheme.typography.bodyMedium,
                    color = MaterialTheme.colorScheme.onSurfaceVariant
                )
            }
        }
    }
}

@Preview(showBackground = true)
@Composable
fun InviteFriendScreenPreview() {
    AppTheme {
        InviteFriendScreen(
            navController = rememberNavController(), 
            viewModel = viewModel()
        )
    }
} 