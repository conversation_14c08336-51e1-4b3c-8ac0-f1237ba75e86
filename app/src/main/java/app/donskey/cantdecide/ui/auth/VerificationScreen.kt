package app.donskey.cantdecide.ui.auth

import androidx.compose.foundation.Image
import androidx.compose.foundation.background
import androidx.compose.foundation.border
import androidx.compose.foundation.clickable
import androidx.compose.foundation.layout.Box
import androidx.compose.foundation.layout.Column
import androidx.compose.foundation.layout.Row
import androidx.compose.foundation.layout.fillMaxSize
import androidx.compose.foundation.layout.fillMaxWidth
import androidx.compose.foundation.layout.height
import androidx.compose.foundation.layout.padding
import androidx.compose.foundation.layout.widthIn
import androidx.compose.foundation.layout.wrapContentHeight
import androidx.compose.foundation.rememberScrollState
import androidx.compose.foundation.shape.RoundedCornerShape
import androidx.compose.foundation.text.BasicTextField
import androidx.compose.foundation.text.KeyboardOptions
import androidx.compose.foundation.verticalScroll
import androidx.compose.material3.Button
import androidx.compose.material3.CircularProgressIndicator
import androidx.compose.material3.MaterialTheme
import androidx.compose.material3.Text
import androidx.compose.material3.TextButton
import androidx.compose.runtime.Composable
import androidx.compose.runtime.LaunchedEffect
import androidx.compose.runtime.getValue
import androidx.compose.runtime.mutableStateOf
import androidx.compose.runtime.remember
import androidx.compose.runtime.setValue
import androidx.compose.ui.Alignment
import androidx.compose.ui.Modifier
import androidx.compose.ui.focus.FocusRequester
import androidx.compose.ui.focus.focusRequester
import androidx.compose.ui.focus.onFocusChanged
import androidx.compose.ui.graphics.Color
import androidx.compose.ui.input.key.Key
import androidx.compose.ui.input.key.KeyEventType
import androidx.compose.ui.input.key.key
import androidx.compose.ui.input.key.onKeyEvent
import androidx.compose.ui.input.key.type
import androidx.compose.ui.res.painterResource
import androidx.compose.ui.res.stringResource
import androidx.compose.ui.text.TextStyle
import androidx.compose.ui.text.font.FontWeight
import androidx.compose.ui.text.input.KeyboardType
import androidx.compose.ui.text.style.TextAlign
import androidx.compose.ui.unit.dp
import androidx.compose.ui.unit.sp
import app.donskey.cantdecide.R

/**
 * Composable function for the verification screen.
 * Allows users to enter the verification code sent to their phone.
 *
 * @param phoneNumber The phone number that was verified
 * @param onVerifyClick Callback when verification button is clicked with the entered code
 * @param onResendClick Callback when resend code button is clicked
 * @param onChangeNumberClick Callback when change number button is clicked
 * @param isLoading Whether the screen is in a loading state
 * @param errorMessage Error message to display, if any
 * @param resendTimeRemaining Time remaining before code can be resent (in seconds)
 */
@Composable
fun VerificationScreen(
    phoneNumber: String,
    onVerifyClick: (code: String) -> Unit,
    onResendClick: () -> Unit,
    onChangeNumberClick: () -> Unit,
    isLoading: Boolean = false,
    errorMessage: String? = null,
    resendTimeRemaining: Int = 0
) {
    // State for OTP code digits
    val otpDigits = remember { List(6) { mutableStateOf("") } }
    val focusRequesters = remember { List(6) { FocusRequester() } }
    
    // Focus the first field when screen is shown
    LaunchedEffect(Unit) {
        focusRequesters[0].requestFocus()
    }
    
    Box(modifier = Modifier.fillMaxSize()) {
        Column(
            modifier = Modifier
                .fillMaxSize()
                .verticalScroll(rememberScrollState())
                .padding(16.dp),
            horizontalAlignment = Alignment.CenterHorizontally
        ) {
            // Logo
            Image(
                painter = painterResource(id = R.drawable.logo_heading),
                contentDescription = stringResource(R.string.app_logo),
                modifier = Modifier
                    .padding(top = 8.dp)
                    .widthIn(max = 200.dp)
                    .wrapContentHeight()
            )
            
            // Title and message
            Text(
                text = stringResource(R.string.verification_title),
                modifier = Modifier.padding(top = 8.dp),
                style = MaterialTheme.typography.headlineMedium,
                fontWeight = FontWeight.Bold,
                textAlign = TextAlign.Center
            )
            
            Text(
                text = stringResource(R.string.verification_subtitle, phoneNumber),
                modifier = Modifier.padding(top = 4.dp),
                style = MaterialTheme.typography.bodyMedium,
                textAlign = TextAlign.Center
            )
            
            // OTP Input Fields
            Row(
                modifier = Modifier
                    .fillMaxWidth()
                    .padding(top = 16.dp),
                verticalAlignment = Alignment.CenterVertically
            ) {
                for (i in 0 until 6) {
                    OtpDigitInput(
                        value = otpDigits[i].value,
                        onValueChanged = { newValue ->
                            if (newValue.length <= 1) {
                                otpDigits[i].value = newValue
                                
                                // Move focus to next field if digit was entered
                                if (newValue.isNotEmpty() && i < 5) {
                                    focusRequesters[i + 1].requestFocus()
                                }
                            }
                        },
                        onBackspace = {
                            // Move focus to previous field if backspace pressed on empty field
                            if (otpDigits[i].value.isEmpty() && i > 0) {
                                focusRequesters[i - 1].requestFocus()
                            }
                        },
                        focusRequester = focusRequesters[i],
                        modifier = Modifier
                            .weight(1f)
                            .padding(horizontal = 4.dp)
                    )
                }
            }
            
            // Error message
            if (!errorMessage.isNullOrEmpty()) {
                Text(
                    text = errorMessage,
                    modifier = Modifier
                        .padding(top = 12.dp)
                        .fillMaxWidth(),
                    color = MaterialTheme.colorScheme.error,
                    textAlign = TextAlign.Center,
                    style = MaterialTheme.typography.bodyMedium.copy(
                        fontWeight = FontWeight.Bold
                    )
                )
            }
            
            // Resend code
            Text(
                text = stringResource(R.string.resend_code_button),
                modifier = Modifier
                    .padding(top = 8.dp)
                    .clickable(enabled = resendTimeRemaining == 0) { 
                        if (resendTimeRemaining == 0) onResendClick() 
                    }
                    .padding(8.dp),
                color = if (resendTimeRemaining == 0) 
                    MaterialTheme.colorScheme.primary 
                else 
                    MaterialTheme.colorScheme.primary.copy(alpha = 0.5f),
                style = MaterialTheme.typography.bodyMedium
            )
            
            // Timer
            if (resendTimeRemaining > 0) {
                Text(
                    text = stringResource(R.string.resend_timer_text, resendTimeRemaining),
                    modifier = Modifier.padding(top = 4.dp),
                    style = MaterialTheme.typography.bodyMedium
                )
            }
            
            // Verify button
            Button(
                onClick = {
                    val code = otpDigits.joinToString("") { it.value }
                    onVerifyClick(code)
                },
                modifier = Modifier
                    .fillMaxWidth()
                    .padding(top = 16.dp)
                    .height(56.dp),
                enabled = otpDigits.all { it.value.isNotEmpty() } && !isLoading
            ) {
                Text(
                    text = stringResource(R.string.verify_button),
                    fontSize = 16.sp
                )
            }
            
            // Change number button
            TextButton(
                onClick = onChangeNumberClick,
                modifier = Modifier.padding(top = 4.dp),
                enabled = !isLoading
            ) {
                Text(
                    text = stringResource(R.string.change_number_button),
                    color = MaterialTheme.colorScheme.primary
                )
            }
        }
        
        // Loading overlay
        if (isLoading) {
            Box(
                modifier = Modifier
                    .fillMaxSize()
                    .background(Color.Black.copy(alpha = 0.5f))
                    .clickable(enabled = false) { /* Prevent clicks through overlay */ },
                contentAlignment = Alignment.Center
            ) {
                CircularProgressIndicator(color = Color.White)
            }
        }
    }
}

/**
 * Single digit input for OTP code.
 *
 * @param value Current value of the digit
 * @param onValueChanged Callback when value changes
 * @param onBackspace Callback when backspace is pressed on an empty field
 * @param focusRequester FocusRequester to control focus
 * @param modifier Modifier for styling
 */
@Composable
fun OtpDigitInput(
    value: String,
    onValueChanged: (String) -> Unit,
    onBackspace: () -> Unit,
    focusRequester: FocusRequester,
    modifier: Modifier = Modifier
) {
    var isFocused by remember { mutableStateOf(false) }
    
    Box(
        modifier = modifier.height(48.dp),
        contentAlignment = Alignment.Center
    ) {
        BasicTextField(
            value = value,
            onValueChange = onValueChanged,
            singleLine = true,
            keyboardOptions = KeyboardOptions(
                keyboardType = KeyboardType.Number
            ),
            modifier = Modifier
                .fillMaxSize()
                .focusRequester(focusRequester)
                .onFocusChanged { isFocused = it.isFocused }
                .onKeyEvent { keyEvent ->
                    if (keyEvent.key == Key.Backspace && 
                        keyEvent.type == KeyEventType.KeyUp && 
                        value.isEmpty()
                    ) {
                        onBackspace()
                        true
                    } else {
                        false
                    }
                },
            textStyle = TextStyle(
                color = MaterialTheme.colorScheme.onSurface,
                fontSize = 20.sp,
                fontWeight = FontWeight.Bold,
                textAlign = TextAlign.Center
            ),
            decorationBox = { innerTextField ->
                Box(
                    modifier = Modifier
                        .fillMaxSize()
                        .background(
                            color = MaterialTheme.colorScheme.surface,
                            shape = RoundedCornerShape(4.dp)
                        )
                        .border(
                            width = if (isFocused) 2.dp else 1.dp,
                            color = if (isFocused) 
                                MaterialTheme.colorScheme.primary 
                            else 
                                MaterialTheme.colorScheme.outline,
                            shape = RoundedCornerShape(4.dp)
                        )
                        .padding(horizontal = 8.dp),
                    contentAlignment = Alignment.Center
                ) {
                    innerTextField()
                }
            }
        )
    }
} 