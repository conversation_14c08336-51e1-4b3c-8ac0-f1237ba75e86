package app.donskey.cantdecide.ui.splash

import android.annotation.SuppressLint
import android.content.Intent
import android.os.Bundle
import android.os.Handler
import android.os.Looper
import android.util.Log
import androidx.activity.ComponentActivity
import app.donskey.cantdecide.MainActivity
import app.donskey.cantdecide.R
import app.donskey.cantdecide.data.repositories.UserRepository
import app.donskey.cantdecide.ui.auth.LoginActivity
import dagger.hilt.android.AndroidEntryPoint
import javax.inject.Inject

/**
 * Splash screen activity that displays when the app is loading.
 * Shows the app logo and checks Firebase connectivity before proceeding to LoginActivity or MainActivity.
 */
@SuppressLint("CustomSplashScreen")
@AndroidEntryPoint
class SplashActivity : ComponentActivity() {
    
    @Inject lateinit var userRepository: UserRepository
    
    companion object {
        private const val TAG = "SplashActivity"
        private const val SPLASH_DELAY = 1000L // 1 second delay
    }
    
    override fun onCreate(savedInstanceState: Bundle?) {
        super.onCreate(savedInstanceState)
        // Set the theme to the splash screen theme to prevent any flashing
        setTheme(R.style.Theme_Cantdecide_NoActionBar) 
        // No need to set content view for a splash screen that just navigates

        Log.d(TAG, "onCreate: Splash screen started.")

        // Use a Handler to delay navigation
        Handler(Looper.getMainLooper()).postDelayed({
            // Check if the user is logged in and has completed profile setup
            if (userRepository.isUserLoggedIn()) { // Use injected userRepository
                Log.d(TAG, "User is logged in. Navigating to MainActivity.")
                startActivity(Intent(this, MainActivity::class.java))
            } else {
                Log.d(TAG, "User is not logged in. Navigating to LoginActivity.")
                startActivity(Intent(this, LoginActivity::class.java))
            }
            finish() // Finish SplashActivity so it's not in the back stack
        }, SPLASH_DELAY)
    }
} 