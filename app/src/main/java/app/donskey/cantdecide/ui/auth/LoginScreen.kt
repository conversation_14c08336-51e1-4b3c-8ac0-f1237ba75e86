package app.donskey.cantdecide.ui.auth

import androidx.compose.foundation.Image
import androidx.compose.foundation.background
import androidx.compose.foundation.clickable
import androidx.compose.foundation.layout.Box
import androidx.compose.foundation.layout.Column
import androidx.compose.foundation.layout.Row
import androidx.compose.foundation.layout.fillMaxSize
import androidx.compose.foundation.layout.fillMaxWidth
import androidx.compose.foundation.layout.height
import androidx.compose.foundation.layout.padding
import androidx.compose.foundation.layout.widthIn
import androidx.compose.foundation.layout.wrapContentHeight
import androidx.compose.foundation.rememberScrollState
import androidx.compose.foundation.verticalScroll
import androidx.compose.material3.Button
import androidx.compose.material3.CircularProgressIndicator
import androidx.compose.material3.MaterialTheme
import androidx.compose.material3.OutlinedTextField
import androidx.compose.material3.Text
import androidx.compose.runtime.Composable
import androidx.compose.runtime.getValue
import androidx.compose.runtime.mutableStateOf
import androidx.compose.runtime.remember
import androidx.compose.runtime.setValue
import androidx.compose.ui.Alignment
import androidx.compose.ui.Modifier
import androidx.compose.ui.graphics.Color
import androidx.compose.ui.res.painterResource
import androidx.compose.ui.res.stringResource
import androidx.compose.ui.text.font.FontWeight
import androidx.compose.ui.text.input.KeyboardType
import androidx.compose.ui.text.style.TextAlign
import androidx.compose.ui.unit.dp
import androidx.compose.ui.unit.sp
import androidx.compose.foundation.text.KeyboardOptions
import android.content.Context
import androidx.compose.ui.viewinterop.AndroidView
import app.donskey.cantdecide.R
import com.hbb20.CountryCodePicker

/**
 * Composable function for the login screen.
 * 
 * @param onPhoneNumberSubmit Callback when user submits their phone number
 * @param isLoading Whether the app is currently loading/processing
 * @param errorMessage Optional error message to display
 */
@Composable
fun LoginScreen(
    onPhoneNumberSubmit: (phoneNumber: String, countryCode: String) -> Unit,
    isLoading: Boolean = false,
    errorMessage: String? = null
) {
    // State for the phone number input
    var phoneNumber by remember { mutableStateOf("") }
    var phoneNumberError by remember { mutableStateOf<String?>(null) }
    var selectedCountryCode by remember { mutableStateOf("+61") } // Default to Australia
    
    Box(modifier = Modifier.fillMaxSize()) {
        Column(
            modifier = Modifier
                .fillMaxSize()
                .verticalScroll(rememberScrollState())
                .padding(16.dp),
            horizontalAlignment = Alignment.CenterHorizontally
        ) {
            // Logo
            Image(
                painter = painterResource(id = R.drawable.logo_heading),
                contentDescription = stringResource(R.string.app_logo),
                modifier = Modifier
                    .padding(top = 8.dp)
                    .widthIn(max = 200.dp)
                    .wrapContentHeight()
            )
            
            // Title and subtitle
            Text(
                text = stringResource(R.string.login_title),
                modifier = Modifier.padding(top = 8.dp),
                style = MaterialTheme.typography.headlineMedium,
                fontWeight = FontWeight.Bold,
                textAlign = TextAlign.Center
            )
            
            Text(
                text = stringResource(R.string.login_subtitle),
                modifier = Modifier.padding(top = 4.dp),
                style = MaterialTheme.typography.bodyMedium,
                textAlign = TextAlign.Center
            )
            
            // Phone number section
            Column(
                modifier = Modifier
                    .fillMaxWidth()
                    .padding(top = 16.dp)
            ) {
                Text(
                    text = stringResource(R.string.phone_number_label),
                    style = MaterialTheme.typography.bodyMedium
                )
                
                Row(
                    modifier = Modifier
                        .fillMaxWidth()
                        .padding(top = 4.dp),
                    verticalAlignment = Alignment.CenterVertically
                ) {
                    // Country code picker (using AndroidView to wrap the existing component)
                    AndroidView(
                        factory = { context ->
                            createCountryCodePicker(context) { countryCode ->
                                selectedCountryCode = countryCode
                            }
                        },
                        modifier = Modifier.padding(end = 4.dp)
                    )
                    
                    OutlinedTextField(
                        value = phoneNumber,
                        onValueChange = { 
                            if (it.length <= 10) {
                                phoneNumber = it
                                phoneNumberError = null
                            }
                        },
                        label = { Text(stringResource(R.string.phone_number_hint)) },
                        modifier = Modifier
                            .weight(1f),
                        keyboardOptions = KeyboardOptions(
                            keyboardType = KeyboardType.Phone
                        ),
                        isError = phoneNumberError != null,
                        supportingText = phoneNumberError?.let { { Text(it) } }
                    )
                }
            }
            
            // Display error message from activity if it exists
            if (!errorMessage.isNullOrBlank()) {
                Text(
                    text = errorMessage,
                    color = MaterialTheme.colorScheme.error,
                    style = MaterialTheme.typography.bodyMedium,
                    modifier = Modifier.padding(top = 8.dp),
                    textAlign = TextAlign.Center
                )
            }

            // Continue button
            Button(
                onClick = {
                    if (phoneNumber.isBlank()) {
                        phoneNumberError = "Please enter your phone number"
                    } else {
                        onPhoneNumberSubmit(phoneNumber, selectedCountryCode)
                    }
                },
                modifier = Modifier
                    .fillMaxWidth()
                    .padding(top = 16.dp)
                    .height(56.dp)
            ) {
                Text(
                    text = stringResource(R.string.continue_button),
                    fontSize = 16.sp
                )
            }
        }
        
        // Loading overlay
        if (isLoading) {
            Box(
                modifier = Modifier
                    .fillMaxSize()
                    .background(Color.Black.copy(alpha = 0.5f))
                    .clickable(enabled = false) { /* Prevent clicks through overlay */ },
                contentAlignment = Alignment.Center
            ) {
                CircularProgressIndicator(color = Color.White)
            }
        }
    }
}

/**
 * Helper function to create a CountryCodePicker instance
 */
private fun createCountryCodePicker(
    context: Context,
    onCountryCodeSelected: (String) -> Unit
): CountryCodePicker {
    return CountryCodePicker(context).apply {
        setDefaultCountryUsingNameCode("AU")
        setOnCountryChangeListener {
            onCountryCodeSelected(selectedCountryCodeWithPlus)
        }
        // CountryCodePicker API might be different than what we initially tried
        // Let's simplify to just the essential functionality
        
        // Keep the default values that match what was in XML
        // No need to configure additional properties if they're not accessible
        
        // Initialize with default value
        onCountryCodeSelected(selectedCountryCodeWithPlus)
    }
} 