package app.donskey.cantdecide.ui.friends

import androidx.compose.runtime.Composable
import androidx.compose.ui.Modifier
import androidx.navigation.NavHostController
import androidx.navigation.compose.NavHost
import androidx.navigation.compose.composable

/**
 * Navigation routes for the Friends section.
 */
object FriendsDestinations {
    const val FRIENDS_LIST_ROUTE = "friends_list"
    const val INVITE_FRIEND_ROUTE = "invite_friend"
}

/**
 * Friends navigation component that manages navigation between Friend screens.
 */
@Composable
fun FriendsNavigation(
    navController: NavHostController,
    modifier: Modifier = Modifier,
    friendsViewModel: FriendsViewModel
) {
    NavHost(
        navController = navController,
        startDestination = FriendsDestinations.FRIENDS_LIST_ROUTE,
        modifier = modifier
    ) {
        // Friends List screen
        composable(FriendsDestinations.FRIENDS_LIST_ROUTE) {
            FriendsScreen(
                navController = navController,
                viewModel = friendsViewModel
            )
        }
        
        // Invite Friend screen
        composable(FriendsDestinations.INVITE_FRIEND_ROUTE) {
            InviteFriendScreen(
                navController = navController,
                viewModel = friendsViewModel
            )
        }
    }
} 