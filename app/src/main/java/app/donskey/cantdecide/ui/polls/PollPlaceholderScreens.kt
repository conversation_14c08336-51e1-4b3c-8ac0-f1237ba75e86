package app.donskey.cantdecide.ui.polls

import androidx.compose.foundation.layout.Box
import androidx.compose.foundation.layout.Column
import androidx.compose.foundation.layout.Spacer
import androidx.compose.foundation.layout.fillMaxSize
import androidx.compose.foundation.layout.fillMaxWidth
import androidx.compose.foundation.layout.height
import androidx.compose.foundation.layout.padding
import androidx.compose.foundation.rememberScrollState
import androidx.compose.foundation.verticalScroll
import androidx.compose.material.icons.Icons
import androidx.compose.material.icons.automirrored.filled.ArrowBack
import androidx.compose.material3.ExperimentalMaterial3Api
import androidx.compose.material3.Icon
import androidx.compose.material3.IconButton
import androidx.compose.material3.MaterialTheme
import androidx.compose.material3.Scaffold
import androidx.compose.material3.Surface
import androidx.compose.material3.Text
import androidx.compose.material3.TopAppBar
import androidx.compose.runtime.Composable
import androidx.compose.ui.Alignment
import androidx.compose.ui.Modifier
import androidx.compose.ui.text.font.FontWeight
import androidx.compose.ui.text.style.TextAlign
import androidx.compose.ui.tooling.preview.Preview
import androidx.compose.ui.unit.dp
import androidx.navigation.NavController
import androidx.navigation.compose.rememberNavController
import app.donskey.cantdecide.ui.theme.AppTheme

/**
 * Placeholder screen for the Private Text Poll feature.
 * This will be replaced with actual functionality in future development.
 *
 * @param navController Navigation controller for navigating back.
 */
@OptIn(ExperimentalMaterial3Api::class)
@Composable
fun PrivateTextPollScreen(navController: NavController) {
    PollTypeScreen(
        title = "Create Private Text Poll",
        description = "This feature will allow you to create a text-based poll that can be shared with selected friends.",
        navController = navController
    )
}

/**
 * Placeholder screen for the Private Image Poll feature.
 * This will be replaced with actual functionality in future development.
 *
 * @param navController Navigation controller for navigating back.
 */
@OptIn(ExperimentalMaterial3Api::class)
@Composable
fun PrivateImagePollScreen(navController: NavController) {
    PollTypeScreen(
        title = "Create Private Image Poll",
        description = "This feature will allow you to create an image-based poll that can be shared with selected friends.",
        navController = navController
    )
}

/**
 * Placeholder screen for the Private Video Poll feature.
 * This will be replaced with actual functionality in future development.
 *
 * @param navController Navigation controller for navigating back.
 */
@OptIn(ExperimentalMaterial3Api::class)
@Composable
fun PrivateVideoPollScreen(navController: NavController) {
    PollTypeScreen(
        title = "Create Private Video Poll",
        description = "This feature will allow you to create a video-based poll that can be shared with selected friends.",
        navController = navController
    )
}

/**
 * Placeholder screen for the Public Text Poll feature.
 * This will be replaced with actual functionality in future development.
 *
 * @param navController Navigation controller for navigating back.
 */
@OptIn(ExperimentalMaterial3Api::class)
@Composable
fun PublicTextPollScreen(navController: NavController) {
    PollTypeScreen(
        title = "Create Public Text Poll",
        description = "This feature will allow you to create a text-based poll that can be viewed and voted on by any user.",
        navController = navController
    )
}

/**
 * Placeholder screen for the Public Image Poll feature.
 * This will be replaced with actual functionality in future development.
 *
 * @param navController Navigation controller for navigating back.
 */
@OptIn(ExperimentalMaterial3Api::class)
@Composable
fun PublicImagePollScreen(navController: NavController) {
    PollTypeScreen(
        title = "Create Public Image Poll",
        description = "This feature will allow you to create an image-based poll that can be viewed and voted on by any user.",
        navController = navController
    )
}

/**
 * Placeholder screen for the Public Video Poll feature.
 * This will be replaced with actual functionality in future development.
 *
 * @param navController Navigation controller for navigating back.
 */
@OptIn(ExperimentalMaterial3Api::class)
@Composable
fun PublicVideoPollScreen(navController: NavController) {
    PollTypeScreen(
        title = "Create Public Video Poll",
        description = "This feature will allow you to create a video-based poll that can be viewed and voted on by any user.",
        navController = navController
    )
}

/**
 * Generic poll type screen that serves as a template for all poll type placeholders.
 * Provides consistent styling and messaging across all poll type screens.
 *
 * @param title The title to display at the top of the screen.
 * @param description Description of what the feature will do when implemented.
 * @param navController Navigation controller for navigating back.
 */
@OptIn(ExperimentalMaterial3Api::class)
@Composable
fun PollTypeScreen(
    title: String,
    description: String,
    navController: NavController
) {
    // Scroll state for making the content scrollable
    val scrollState = rememberScrollState()
    
    Scaffold(
        topBar = {
            TopAppBar(
                title = { Text(text = title) },
                navigationIcon = {
                    IconButton(onClick = { navController.popBackStack() }) {
                        Icon(
                            imageVector = Icons.AutoMirrored.Filled.ArrowBack,
                            contentDescription = "Navigate back"
                        )
                    }
                }
            )
        }
    ) { paddingValues ->
        Surface(
            modifier = Modifier
                .fillMaxSize()
                .padding(paddingValues),
            color = MaterialTheme.colorScheme.background
        ) {
            // Scrollable column to accommodate future implementation
            Column(
                modifier = Modifier
                    .fillMaxSize()
                    .padding(16.dp)
                    .verticalScroll(scrollState),
                horizontalAlignment = Alignment.CenterHorizontally
            ) {
                Spacer(modifier = Modifier.height(32.dp))
                
                Text(
                    text = title,
                    style = MaterialTheme.typography.headlineMedium,
                    fontWeight = FontWeight.Bold,
                    textAlign = TextAlign.Center
                )
                
                Spacer(modifier = Modifier.height(16.dp))
                
                Text(
                    text = "This feature is coming soon!",
                    style = MaterialTheme.typography.bodyLarge,
                    textAlign = TextAlign.Center
                )
                
                Spacer(modifier = Modifier.height(16.dp))
                
                Text(
                    text = description,
                    style = MaterialTheme.typography.bodyMedium,
                    textAlign = TextAlign.Center,
                    modifier = Modifier.fillMaxWidth(0.8f)
                )
                
                // Additional space for future implementation
                Spacer(modifier = Modifier.height(400.dp))
            }
        }
    }
}

@Preview(showBackground = true)
@Composable
fun PrivateTextPollScreenPreview() {
    AppTheme {
        PrivateTextPollScreen(
            navController = rememberNavController()
        )
    }
}

@Preview(showBackground = true)
@Composable
fun PublicImagePollScreenPreview() {
    AppTheme {
        PublicImagePollScreen(
            navController = rememberNavController()
        )
    }
}

@OptIn(ExperimentalMaterial3Api::class)
@Composable
fun GenericPollPlaceholderScreen(title: String, navController: NavController) {
    Scaffold(
        topBar = {
            TopAppBar(
                title = { Text(title) },
                navigationIcon = {
                    IconButton(onClick = { navController.popBackStack() }) {
                        Icon(Icons.AutoMirrored.Filled.ArrowBack, contentDescription = "Back")
                    }
                }
            )
        }
    ) { paddingValues ->
        Box(
            modifier = Modifier
                .fillMaxSize()
                .padding(paddingValues)
                .padding(16.dp),
            contentAlignment = Alignment.Center
        ) {
            Text(
                text = "$title screen is under development.\nComing Soon!",
                style = MaterialTheme.typography.headlineSmall,
                textAlign = TextAlign.Center
            )
        }
    }
}

@Composable
fun PrivatePollsHistoryScreen(navController: NavController) {
    GenericPollPlaceholderScreen(title = "Private Polls History", navController = navController)
}

// Placeholder for the screen that lets you choose between Text, Image, Video for Public Polls
// This might be similar to CreatePrivatePollScreen.kt
@Composable
fun CreatePublicPollOptionsScreen(navController: NavController) {
    GenericPollPlaceholderScreen(title = "Create Public Poll", navController = navController)
}

@Composable
fun ViewPublicPollsScreen(navController: NavController) {
    GenericPollPlaceholderScreen(title = "View Public Polls", navController = navController)
}

@Composable
fun PublicPollsHistoryScreen(navController: NavController) {
    GenericPollPlaceholderScreen(title = "Public Polls History", navController = navController)
} 