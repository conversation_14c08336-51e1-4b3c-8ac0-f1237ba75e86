package app.donskey.cantdecide.ui.friends

import android.annotation.SuppressLint
import androidx.compose.foundation.clickable
import androidx.compose.foundation.layout.Box
import androidx.compose.foundation.layout.BoxWithConstraints
import androidx.compose.foundation.layout.Row
import androidx.compose.foundation.layout.Spacer
import androidx.compose.foundation.layout.fillMaxSize
import androidx.compose.foundation.layout.fillMaxWidth
import androidx.compose.foundation.layout.height
import androidx.compose.foundation.layout.padding
import androidx.compose.foundation.layout.width
import androidx.compose.foundation.shape.RoundedCornerShape
import androidx.compose.material.icons.Icons
import androidx.compose.material.icons.filled.PersonAdd
import androidx.compose.material.icons.automirrored.filled.ArrowForward
import androidx.compose.material3.Card
import androidx.compose.material3.CardDefaults
import androidx.compose.material3.CircularProgressIndicator
import androidx.compose.material3.Icon
import androidx.compose.material3.MaterialTheme
import androidx.compose.material3.Scaffold
import androidx.compose.material3.SnackbarHost
import androidx.compose.material3.SnackbarHostState
import androidx.compose.material3.Text
import androidx.compose.runtime.Composable
import androidx.compose.runtime.LaunchedEffect
import androidx.compose.runtime.collectAsState
import androidx.compose.runtime.getValue
import androidx.compose.runtime.remember
import androidx.compose.ui.Alignment
import androidx.compose.ui.Modifier
import androidx.compose.ui.res.stringResource
import androidx.compose.ui.text.font.FontWeight
import androidx.compose.ui.text.style.TextAlign
import androidx.compose.ui.tooling.preview.Preview
import androidx.compose.ui.unit.dp
import androidx.lifecycle.viewmodel.compose.viewModel
import androidx.navigation.NavController
import androidx.navigation.compose.rememberNavController
import android.util.Log
import androidx.compose.foundation.lazy.LazyColumn
import androidx.compose.foundation.lazy.items
import androidx.compose.foundation.Image
import androidx.compose.foundation.layout.Arrangement
import androidx.compose.foundation.layout.size
import androidx.compose.foundation.shape.CircleShape
import androidx.compose.material.icons.filled.Check
import androidx.compose.material.icons.filled.Close
import androidx.compose.material.icons.filled.Delete
import androidx.compose.material3.HorizontalDivider
import androidx.compose.material3.IconButton
import androidx.compose.ui.draw.clip
import androidx.compose.ui.layout.ContentScale
import coil.compose.rememberAsyncImagePainter
import androidx.compose.material3.AlertDialog
import androidx.compose.material3.TextButton
import androidx.compose.runtime.mutableStateOf
import androidx.compose.runtime.setValue
import androidx.compose.ui.res.painterResource
import app.donskey.cantdecide.R
import app.donskey.cantdecide.data.models.Friend
import app.donskey.cantdecide.data.models.User
import app.donskey.cantdecide.ui.theme.AppTheme
import androidx.compose.animation.animateContentSize

/**
 * Main screen for the Friends section of the app.
 * Uses a single LazyColumn to display all content for better dynamic layout.
 */
@SuppressLint("UnusedBoxWithConstraintsScope")
@Composable
fun FriendsScreen(
    navController: NavController,
    viewModel: FriendsViewModel
) {
    val uiState by viewModel.uiState.collectAsState()
    val snackbarHostState = remember { SnackbarHostState() }
    var showDeleteDialog by remember { mutableStateOf(false) }
    var friendIdToDelete by remember { mutableStateOf<String?>(null) }
    var friendNameToDelete by remember { mutableStateOf<String?>(null) }

    // Observe the message state for showing Snackbars
    LaunchedEffect(uiState.friendRequestStatusMessage) {
        val message = uiState.friendRequestStatusMessage
        if (message != null) {
            Log.d("FriendsScreen", "Showing Snackbar for message: $message")
            try {
                // Show Snackbar with Long duration directly in the scope
                snackbarHostState.showSnackbar(
                    message = message, 
                    duration = androidx.compose.material3.SnackbarDuration.Long // Ensure duration is Long
                )
            } finally {
                // Important: Clear message after Snackbar potentially finishes showing
                // Consider clearing immediately after triggering if Snackbar duration is long
                // and you want to avoid showing the same message twice if state recomposes.
                // However, clearing in finally ensures it's cleared even if showSnackbar is interrupted.
                viewModel.clearFriendRequestStatusMessage()
            }
        }
    }

    Scaffold(
        snackbarHost = { SnackbarHost(snackbarHostState) },
        content = { paddingValues ->
            // Use BoxWithConstraints to allow centering the main loader overlay
            BoxWithConstraints(
                modifier = Modifier
                    .fillMaxSize()
                    // Apply horizontal padding here if it should be outside the LazyColumn's scrollable area
                    // .padding(horizontal = 16.dp) 
            ) {
                LazyColumn(
                    modifier = Modifier
                        .fillMaxSize()
                        .padding(horizontal = 16.dp), // Keep horizontal padding for list content
                    contentPadding = paddingValues // Apply padding from Scaffold (for system bars)
                ) {
                    item {
                        Spacer(modifier = Modifier.height(24.dp))
                        InviteFriendCard(
                            onClick = { navController.navigate(FriendsDestinations.INVITE_FRIEND_ROUTE) }
                        )
                        Spacer(modifier = Modifier.height(24.dp))
                    }

                    // --- Incoming Friend Requests Section ---
                    if (uiState.isLoadingRequests) {
                        item {
                            Box(modifier = Modifier.fillMaxWidth().padding(vertical = 8.dp), contentAlignment = Alignment.Center) {
                                CircularProgressIndicator()
                            }
                        }
                    } else if (uiState.incomingRequests.isNotEmpty()) {
                        item {
                            Text(
                                text = stringResource(R.string.friend_requests),
                                style = MaterialTheme.typography.titleLarge,
                                fontWeight = FontWeight.Bold,
                                modifier = Modifier.padding(bottom = 8.dp)
                            )
                        }
                        items(uiState.incomingRequests, key = { "incoming_${it.request.id}" }) { requestWithProfile ->
                            val request = requestWithProfile.request
                            val senderProfile = requestWithProfile.senderProfile
                            FriendRequestItem(
                                request = request,
                                sender = senderProfile,
                                onAccept = { viewModel.acceptFriendRequest(request.id) },
                                onReject = { viewModel.rejectFriendRequest(request.id) }
                            )
                            HorizontalDivider()
                        }
                        item { Spacer(modifier = Modifier.height(24.dp)) }
                    }

                    // --- Sent Invitations Section ---
                    if (uiState.isLoadingOutgoingRequests) {
                        item {
                            Box(modifier = Modifier.fillMaxWidth().padding(vertical = 8.dp), contentAlignment = Alignment.Center) {
                                CircularProgressIndicator()
                            }
                        }
                    } else if (uiState.outgoingRequestsWithProfiles.isNotEmpty()) {
                        item {
                            Text(
                                text = stringResource(R.string.sent_invitations),
                                style = MaterialTheme.typography.titleLarge,
                                fontWeight = FontWeight.Bold,
                                modifier = Modifier.padding(bottom = 8.dp)
                            )
                        }
                        items(uiState.outgoingRequestsWithProfiles, key = { "outgoing_${it.request.id}" }) { outgoingRequest ->
                            val recipientProfile = outgoingRequest.senderProfile
                            SentRequestItem(
                                recipientName = recipientProfile?.displayName ?: stringResource(R.string.unknown_user),
                                recipientPhotoUrl = recipientProfile?.photoUrl
                            )
                            HorizontalDivider()
                        }
                        item { Spacer(modifier = Modifier.height(24.dp)) }
                    }

                    // --- Friends List Section ---
                    item {
                        Text(
                            text = stringResource(R.string.friends),
                            style = MaterialTheme.typography.titleLarge,
                            fontWeight = FontWeight.Bold,
                            modifier = Modifier.padding(bottom = 8.dp)
                        )
                    }
                    if (uiState.friends.isNotEmpty()) {
                        items(uiState.friends, key = { "friend_${it.friend.id}" }) { friendWithProfile ->
                            val friend = friendWithProfile.friend
                            val friendProfile = friendWithProfile.profile
                            FriendListItem(
                                currentLoggedInUserId = uiState.currentLoggedInUserId,
                                friend = friend,
                                friendProfile = friendProfile,
                                onRemove = {
                                    friendIdToDelete = friend.id
                                    friendNameToDelete = friendProfile?.displayName
                                    showDeleteDialog = true
                                }
                            )
                            HorizontalDivider()
                        }
                    } else if (!uiState.isLoading && !uiState.isLoadingRequests && !uiState.isLoadingOutgoingRequests) { // Only show if not loading anything
                        item {
                            Text(
                                text = stringResource(R.string.friends_list_empty),
                                style = MaterialTheme.typography.bodyMedium,
                                color = MaterialTheme.colorScheme.onSurfaceVariant,
                                textAlign = TextAlign.Center,
                                modifier = Modifier.fillMaxWidth().padding(vertical = 16.dp)
                            )
                        }
                    }

                    // Error message if any
                    if (uiState.error != null) {
                        item {
                            Text(
                                text = uiState.error ?: "",
                                style = MaterialTheme.typography.bodyMedium,
                                color = MaterialTheme.colorScheme.error,
                                textAlign = TextAlign.Center,
                                modifier = Modifier.fillMaxWidth().padding(vertical = 16.dp)
                            )
                        }
                    }
                    item { Spacer(modifier = Modifier.height(16.dp)) } // Bottom spacer
                }

                // Centered overlay for the main loading indicator
                if (uiState.isLoading && uiState.friends.isEmpty() && uiState.incomingRequests.isEmpty() && uiState.outgoingRequestsWithProfiles.isEmpty()) {
                    CircularProgressIndicator(
                        modifier = Modifier.align(Alignment.Center) // Align to the center of the BoxWithConstraints
                    )
                }
            }
        }
    )

    if (showDeleteDialog) {
        AlertDialog(
            onDismissRequest = { 
                showDeleteDialog = false 
                friendIdToDelete = null
                friendNameToDelete = null
            },
            title = { Text(text = stringResource(R.string.remove_friend_title)) },
            text = {
                Text(
                    text = stringResource(
                        R.string.remove_friend_confirmation,
                        friendNameToDelete ?: stringResource(R.string.unknown_user)
                    )
                )
            },
            confirmButton = {
                TextButton(
                    onClick = {
                        friendIdToDelete?.let { viewModel.removeFriend(it) }
                        showDeleteDialog = false
                        friendIdToDelete = null
                        friendNameToDelete = null
                    }
                ) {
                    Text(stringResource(R.string.confirm))
                }
            },
            dismissButton = {
                TextButton(
                    onClick = {
                        showDeleteDialog = false
                        friendIdToDelete = null
                        friendNameToDelete = null
                    }
                ) {
                    Text(stringResource(R.string.cancel))
                }
            }
        )
    }
}

/**
 * Card component for the "Invite a Friend" option.
 */
@Composable
fun InviteFriendCard(onClick: () -> Unit) {
    Card(
        modifier = Modifier
            .fillMaxWidth()
            .clickable(onClick = onClick),
        elevation = CardDefaults.cardElevation(
            defaultElevation = 4.dp
        ),
        shape = RoundedCornerShape(8.dp),
        colors = CardDefaults.cardColors(containerColor = MaterialTheme.colorScheme.surface)
    ) {
        Row(
            modifier = Modifier
                .fillMaxWidth()
                .padding(16.dp),
            verticalAlignment = Alignment.CenterVertically
        ) {
            Icon(
                imageVector = Icons.Default.PersonAdd,
                contentDescription = stringResource(R.string.invite_a_friend),
                tint = MaterialTheme.colorScheme.primary
            )
            Spacer(modifier = Modifier.width(16.dp))
            Text(
                text = stringResource(R.string.invite_a_friend),
                style = MaterialTheme.typography.bodyLarge,
                color = MaterialTheme.colorScheme.primary,
                modifier = Modifier.weight(1f)
            )
            Icon(
                imageVector = Icons.AutoMirrored.Filled.ArrowForward,
                contentDescription = stringResource(R.string.navigate_to),
                tint = MaterialTheme.colorScheme.primary
            )
        }
    }
}

// --- New Composable for Friend Request Item ---
@Composable
fun FriendRequestItem(
    request: Friend,
    sender: User?,
    onAccept: () -> Unit,
    onReject: () -> Unit
) {
    // Log the received sender profile
    Log.d("FriendsScreen", "FriendRequestItem composing for request ID: ${request.id}, Sender Profile: ${sender?.displayName ?: "null"}")

    Row(
        modifier = Modifier
            .fillMaxWidth()
            .padding(vertical = 8.dp),
        verticalAlignment = Alignment.CenterVertically,
        horizontalArrangement = Arrangement.SpaceBetween // Align items
    ) {
        Row(verticalAlignment = Alignment.CenterVertically, modifier = Modifier.weight(1f)) {
            // Profile picture
            Image(
                painter = rememberAsyncImagePainter(model = sender?.photoUrl ?: R.drawable.default_profile_pic), // Use default if null
                contentDescription = "Sender Profile Picture",
                modifier = Modifier
                    .size(40.dp)
                    .clip(CircleShape),
                contentScale = ContentScale.Crop
            )
            Spacer(modifier = Modifier.width(12.dp))
            // Name
            Text(
                text = sender?.displayName ?: stringResource(R.string.unknown_user),
                style = MaterialTheme.typography.bodyLarge,
                maxLines = 1 // Prevent text wrapping issues
            )
        }
        // Action buttons
        Row {
            IconButton(onClick = onAccept) {
                Icon(Icons.Default.Check, contentDescription = "Accept Request", tint = MaterialTheme.colorScheme.primary)
            }
            IconButton(onClick = onReject) {
                Icon(Icons.Default.Close, contentDescription = "Reject Request", tint = MaterialTheme.colorScheme.error)
            }
        }
    }
}

// --- New Composable for Friend List Item ---
@Composable
fun FriendListItem(
    currentLoggedInUserId: String?,
    friend: Friend,
    friendProfile: User?,
    onRemove: () -> Unit
) {
    // Log the received friend profile
    Log.d("FriendsScreen", "FriendListItem composing for friend ID: ${friend.id}, Friend Profile: ${friendProfile?.displayName ?: "null"}")

    Row(
        modifier = Modifier
            .fillMaxWidth()
            .padding(vertical = 8.dp)
            .animateContentSize(),
        verticalAlignment = Alignment.CenterVertically,
        horizontalArrangement = Arrangement.SpaceBetween // Align items
    ) {
        Row(verticalAlignment = Alignment.CenterVertically, modifier = Modifier.weight(1f)) {
            // Profile picture
            Image(
                painter = rememberAsyncImagePainter(model = friendProfile?.photoUrl ?: R.drawable.default_profile_pic),
                contentDescription = "Friend Profile Picture",
                modifier = Modifier
                    .size(40.dp)
                    .clip(CircleShape),
                contentScale = ContentScale.Crop
            )
            Spacer(modifier = Modifier.width(12.dp))
            // Name
            Text(
                text = friendProfile?.displayName ?: stringResource(R.string.unknown_user),
                style = MaterialTheme.typography.bodyLarge,
                maxLines = 1 // Prevent text wrapping issues
            )
            // Display "Offline" status if the friend has chosen to hide their online status
            if (currentLoggedInUserId != null) {
                val otherUserId = friend.getOtherUserId(currentLoggedInUserId)
                // Get visibility status from the friend object's participantVisibilities map
                // Default to false (visible) if the other user's UID is not in the map or visibility is not set
                val isOtherUserHidden = friend.participantVisibilities[otherUserId] ?: false

                if (isOtherUserHidden) {
                    Spacer(modifier = Modifier.width(8.dp))
                    Text(
                        text = stringResource(R.string.status_offline),
                        style = MaterialTheme.typography.bodySmall,
                        color = MaterialTheme.colorScheme.error
                    )
                }
            }
        }
        // Remove button (simple for now)
        IconButton(onClick = onRemove) {
            Icon(Icons.Default.Delete, contentDescription = "Remove Friend", tint = MaterialTheme.colorScheme.error)
        }
    }
}

/**
 * Composable item to display a sent friend request.
 */
@Composable
fun SentRequestItem(
    recipientName: String,
    recipientPhotoUrl: String?
    // onCancel: () -> Unit // Optional: For a cancel button
) {
    Card(
        modifier = Modifier
            .fillMaxWidth()
            .padding(vertical = 4.dp),
        elevation = CardDefaults.cardElevation(defaultElevation = 2.dp),
        shape = RoundedCornerShape(8.dp)
    ) {
        Row(
            modifier = Modifier
                .fillMaxWidth()
                .padding(12.dp),
            verticalAlignment = Alignment.CenterVertically,
            horizontalArrangement = Arrangement.SpaceBetween
        ) {
            Row(verticalAlignment = Alignment.CenterVertically) {
                Image(
                    painter = rememberAsyncImagePainter(
                        model = recipientPhotoUrl ?: R.drawable.default_profile_pic, // Changed to default_profile_pic
                        // Optional: Add error/placeholder drawables for Coil
                         error = painterResource(id = R.drawable.default_profile_pic), // Changed to default_profile_pic
                         placeholder = painterResource(id = R.drawable.default_profile_pic) // Changed to default_profile_pic
                    ),
                    contentDescription = "Recipient Profile Picture",
                    modifier = Modifier
                        .size(40.dp)
                        .clip(CircleShape),
                    contentScale = ContentScale.Crop
                )
                Spacer(modifier = Modifier.width(12.dp))
                Text(
                    text = recipientName,
                    style = MaterialTheme.typography.bodyLarge,
                    fontWeight = FontWeight.Medium
                )
            }
            // Optional: Add a Cancel button here if needed
            // IconButton(onClick = onCancel) {
            //     Icon(Icons.Default.Cancel, contentDescription = "Cancel Request") // Example icon
            // }
        }
    }
}

@Preview(showBackground = true)
@Composable
fun FriendsScreenPreview() {
    AppTheme {
        FriendsScreen(
            navController = rememberNavController(), 
            viewModel = viewModel() 
        )
    }
}

@Preview(showBackground = true)
@Composable
fun InviteFriendCardPreview() {
    AppTheme {
        InviteFriendCard(onClick = {})
    }
} 