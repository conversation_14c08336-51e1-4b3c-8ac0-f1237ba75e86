package app.donskey.cantdecide.ui.polls

import android.util.Log
import androidx.compose.foundation.layout.*
import androidx.compose.foundation.rememberScrollState
import androidx.compose.foundation.shape.RoundedCornerShape
import androidx.compose.foundation.verticalScroll
import androidx.compose.material.icons.Icons
import androidx.compose.material.icons.automirrored.filled.ListAlt
import androidx.compose.material.icons.filled.AddCircleOutline
import androidx.compose.material.icons.filled.History
import androidx.compose.material3.*
import androidx.compose.runtime.Composable
import androidx.compose.ui.Alignment
import androidx.compose.ui.Modifier
import androidx.compose.ui.text.font.FontWeight
import androidx.compose.ui.tooling.preview.Preview
import androidx.compose.ui.unit.dp
import androidx.navigation.NavController
import androidx.navigation.compose.rememberNavController
import app.donskey.cantdecide.ui.theme.AppTheme
import app.donskey.cantdecide.ui.settings.SettingsItemRow

/**
 * Main screen for the Polls section of the app.
 * This is a placeholder until the full polls implementation is completed.
 */

// Navigation Routes (consider moving to a central navigation file/object)
object PollsDestinations {
    const val CREATE_PRIVATE_POLL_OPTIONS = "create_private_poll_options_screen" // Navigates to CreatePrivatePollScreen.kt
    const val VIEW_PRIVATE_TEXT_POLLS = "view_private_text_polls_screen"
    const val PRIVATE_POLLS_HISTORY = "private_polls_history_screen"
    const val CREATE_PUBLIC_POLL_OPTIONS = "create_public_poll_options_screen" // Navigates to a new hub for public poll types
    const val VIEW_PUBLIC_POLLS = "view_public_polls_screen"
    const val PUBLIC_POLLS_HISTORY = "public_polls_history_screen"
}

@OptIn(ExperimentalMaterial3Api::class)
@Composable
fun PollsScreen(navController: NavController) {
    // Log when PollsScreen is composed
    Log.d("PollsScreen", "PollsScreen Composed")

    val scrollState = rememberScrollState()
    Scaffold(
        topBar = {
            TopAppBar(
                title = { Text("Polls") },
                colors = TopAppBarDefaults.topAppBarColors(
                    containerColor = MaterialTheme.colorScheme.primary,
                    titleContentColor = MaterialTheme.colorScheme.onPrimary
                )
            )
        }
    ) { innerPadding ->
        Surface(
            modifier = Modifier
                .fillMaxSize()
                .padding(innerPadding),
            color = MaterialTheme.colorScheme.background
        ) {
            Column(
                modifier = Modifier
                    .fillMaxSize()
                    .padding(16.dp)
                    .verticalScroll(scrollState),
                horizontalAlignment = Alignment.CenterHorizontally
            ) {
                // Private Polls Card
                Card(
                    modifier = Modifier
                        .fillMaxWidth()
                        .padding(bottom = 16.dp),
                    shape = RoundedCornerShape(16.dp),
                    elevation = CardDefaults.cardElevation(defaultElevation = 4.dp),
                    colors = CardDefaults.cardColors(
                        containerColor = MaterialTheme.colorScheme.surface
                    )
                ) {
                    Column(
                        modifier = Modifier.padding(vertical = 8.dp)
                    ) {
                        Text(
                            text = "Private Polls",
                            style = MaterialTheme.typography.titleMedium,
                            fontWeight = FontWeight.Bold,
                            modifier = Modifier.padding(horizontal = 16.dp, vertical = 8.dp)
                        )
                        
                        // Create Private Polls
                        SettingsItemRow(
                            icon = Icons.Filled.AddCircleOutline,
                            title = "Create Private Polls",
                            onClick = { navController.navigate(PollsDestinations.CREATE_PRIVATE_POLL_OPTIONS) }
                        )
                        
                        HorizontalDivider(
                            modifier = Modifier.padding(horizontal = 16.dp),
                            thickness = 0.5.dp,
                            color = MaterialTheme.colorScheme.outlineVariant
                        )
                        
                        // View Private Polls
                        SettingsItemRow(
                            icon = Icons.AutoMirrored.Filled.ListAlt,
                            title = "View Private Polls",
                            onClick = { navController.navigate(PollsDestinations.VIEW_PRIVATE_TEXT_POLLS) }
                        )
                        
                        HorizontalDivider(
                            modifier = Modifier.padding(horizontal = 16.dp),
                            thickness = 0.5.dp,
                            color = MaterialTheme.colorScheme.outlineVariant
                        )
                        
                        // Private Polls History
                        SettingsItemRow(
                            icon = Icons.Filled.History,
                            title = "Private Polls History",
                            onClick = { navController.navigate(PollsDestinations.PRIVATE_POLLS_HISTORY) }
                        )
                    }
                }

                // Public Polls Card
                Card(
                    modifier = Modifier
                        .fillMaxWidth()
                        .padding(bottom = 16.dp),
                    shape = RoundedCornerShape(16.dp),
                    elevation = CardDefaults.cardElevation(defaultElevation = 4.dp),
                    colors = CardDefaults.cardColors(
                        containerColor = MaterialTheme.colorScheme.surface
                    )
                ) {
                    Column(
                        modifier = Modifier.padding(vertical = 8.dp)
                    ) {
                        Text(
                            text = "Public Polls",
                            style = MaterialTheme.typography.titleMedium,
                            fontWeight = FontWeight.Bold,
                            modifier = Modifier.padding(horizontal = 16.dp, vertical = 8.dp)
                        )
                        
                        // Create Public Polls
                        SettingsItemRow(
                            icon = Icons.Filled.AddCircleOutline,
                            title = "Create Public Polls",
                            onClick = { navController.navigate(PollsDestinations.CREATE_PUBLIC_POLL_OPTIONS) }
                        )
                        
                        HorizontalDivider(
                            modifier = Modifier.padding(horizontal = 16.dp),
                            thickness = 0.5.dp,
                            color = MaterialTheme.colorScheme.outlineVariant
                        )
                        
                        // View Public Polls
                        SettingsItemRow(
                            icon = Icons.AutoMirrored.Filled.ListAlt,
                            title = "View Public Polls",
                            onClick = { navController.navigate(PollsDestinations.VIEW_PUBLIC_POLLS) }
                        )
                        
                        HorizontalDivider(
                            modifier = Modifier.padding(horizontal = 16.dp),
                            thickness = 0.5.dp,
                            color = MaterialTheme.colorScheme.outlineVariant
                        )
                        
                        // Public Polls History
                        SettingsItemRow(
                            icon = Icons.Filled.History,
                            title = "Public Polls History",
                            onClick = { navController.navigate(PollsDestinations.PUBLIC_POLLS_HISTORY) }
                        )
                    }
                }
                
                // Add space at the bottom for better scrolling experience
                Spacer(modifier = Modifier.height(80.dp))
            }
        }
    }
}

@Preview(showBackground = true)
@Composable
fun PollsScreenPreview() {
    AppTheme {
        PollsScreen(navController = rememberNavController())
    }
} 