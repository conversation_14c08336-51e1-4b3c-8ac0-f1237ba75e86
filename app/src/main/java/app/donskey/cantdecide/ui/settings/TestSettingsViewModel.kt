package app.donskey.cantdecide.ui.settings

import androidx.lifecycle.ViewModel
import app.donskey.cantdecide.data.repositories.UserRepository
import dagger.hilt.android.lifecycle.HiltViewModel
import javax.inject.Inject
import android.util.Log

@HiltViewModel
class TestSettingsViewModel 
@Inject constructor(
    private val userRepository: UserRepository
) : ViewModel() {
    init {
        Log.d("HiltTest", "TestSettingsViewModel initialized with UserRepository: $userRepository")
    }
    val testMessage: String = "TestSettingsViewModel Loaded Successfully"
} 