package app.donskey.cantdecide.ui.polls

import androidx.compose.foundation.layout.fillMaxSize
import androidx.compose.runtime.Composable
import androidx.compose.ui.Modifier
import androidx.navigation.NavHostController
import androidx.navigation.compose.NavHost
import androidx.navigation.compose.composable
import app.donskey.cantdecide.presentation.features.polls.create_private_text_poll.CreatePrivateTextPollScreen

/**
 * Navigation container for the Create Poll section of the app.
 * Handles navigation between different create poll screens.
 * 
 * @param navController Navigation controller for navigating between screens.
 * @param modifier Modifier for customizing the layout.
 */
@Composable
fun CreatePollNavigation(
    navController: NavHostController,
    modifier: Modifier = Modifier
) {
    NavHost(
        navController = navController,
        startDestination = "create_poll_main",
        modifier = modifier.fillMaxSize()
    ) {
        composable("create_poll_main") {
            CreatePollMainScreen(navController = navController)
        }
        
        // Create Poll Selection Screens
        composable("create_private_poll") {
            CreatePrivatePollScreen(navController = navController)
        }
        
        composable("create_public_poll") {
            CreatePublicPollScreen(navController = navController)
        }
        
        // Private Poll Types
        composable("private_text_poll") {
            CreatePrivateTextPollScreen(navController = navController)
        }
        
        composable("private_image_poll") {
            PrivateImagePollScreen(navController = navController)
        }
        
        composable("private_video_poll") {
            PrivateVideoPollScreen(navController = navController)
        }
        
        // Public Poll Types
        composable("public_text_poll") {
            PublicTextPollScreen(navController = navController)
        }
        
        composable("public_image_poll") {
            PublicImagePollScreen(navController = navController)
        }
        
        composable("public_video_poll") {
            PublicVideoPollScreen(navController = navController)
        }
        
        // Other placeholder screens
        composable("view_private_polls") {
            PlaceholderScreen(title = "View Private Polls", navController = navController)
        }
        
        composable("private_polls_history") {
            PlaceholderScreen(title = "Private Polls History", navController = navController)
        }
        
        composable("view_public_polls") {
            PlaceholderScreen(title = "View Public Polls", navController = navController)
        }
        
        composable("public_polls_history") {
            PlaceholderScreen(title = "Public Polls History", navController = navController)
        }
    }
} 