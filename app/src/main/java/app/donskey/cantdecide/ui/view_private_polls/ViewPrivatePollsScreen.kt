package app.donskey.cantdecide.ui.view_private_polls

import androidx.compose.foundation.layout.*
import androidx.compose.foundation.lazy.LazyColumn
import androidx.compose.foundation.lazy.items
import androidx.compose.foundation.shape.RoundedCornerShape
import androidx.compose.material.icons.Icons
import androidx.compose.material.icons.automirrored.filled.ArrowBack
import androidx.compose.material.icons.filled.Delete
import androidx.compose.material.icons.filled.HowToVote
import androidx.compose.material.icons.filled.Info
import androidx.compose.material.icons.filled.Person
import androidx.compose.material.icons.filled.Poll
import androidx.compose.material.icons.filled.Timer
import androidx.compose.material3.*
import androidx.compose.runtime.Composable
import androidx.compose.runtime.LaunchedEffect
import androidx.compose.runtime.collectAsState
import androidx.compose.runtime.getValue
import androidx.compose.runtime.remember
import androidx.compose.ui.Alignment
import androidx.compose.ui.Modifier
import androidx.compose.ui.graphics.Color
import androidx.compose.ui.res.painterResource
import androidx.compose.ui.text.font.FontWeight
import androidx.compose.ui.text.style.TextOverflow
import androidx.compose.ui.tooling.preview.Preview
import androidx.compose.ui.unit.dp
import androidx.hilt.navigation.compose.hiltViewModel
import com.google.firebase.Timestamp
import kotlinx.coroutines.flow.collectLatest
import java.text.SimpleDateFormat
import java.util.Locale
import coil.compose.AsyncImage
import androidx.compose.foundation.shape.CircleShape
import androidx.compose.ui.draw.clip
import app.donskey.cantdecide.R
import androidx.navigation.NavController
import app.donskey.cantdecide.POLL_DETAILS_ROUTE_BASE
import androidx.navigation.compose.currentBackStackEntryAsState

// This is the main composable function for the "View Private Polls" screen.
// It observes the UI state from the ViewModel and displays the list of private polls,
// loading indicators, empty states, or error messages accordingly.
// It also handles user interactions like navigating back or clicking on a poll item.
@OptIn(ExperimentalMaterial3Api::class)
@Composable
fun ViewPrivatePollsScreen(
    // The ViewModel responsible for providing data and handling logic for this screen.
    // It is injected using Hilt's hiltViewModel().
    viewModel: ViewPrivatePollsViewModel = hiltViewModel(),
    navController: NavController,
    // A callback function to be invoked when the user wants to navigate back (e.g., presses the back button in the app bar).
    onNavigateBack: () -> Unit,
) {
    val uiState by viewModel.uiState.collectAsState()
    val pollIdPendingDeletion by viewModel.pollIdPendingDeletion.collectAsState()
    val snackbarHostState = remember { SnackbarHostState() }

    // Handles effects from the ViewModel, like navigation or showing messages.
    LaunchedEffect(key1 = viewModel.uiEffect) {
        viewModel.uiEffect.collectLatest { effect ->
            when (effect) {
                is ViewPrivatePollsEffect.NavigateToPollDetails -> {
                    navController.navigate("$POLL_DETAILS_ROUTE_BASE/${effect.pollId}")
                }
                is ViewPrivatePollsEffect.ShowMessage -> {
                    snackbarHostState.showSnackbar(effect.message)
                }
            }
        }
    }

    // Show AlertDialog when pollIdPendingDeletion is not null
    if (pollIdPendingDeletion != null) {
        AlertDialog(
            onDismissRequest = { viewModel.handleEvent(ViewPrivatePollsEvent.DismissPollDeletionDialog) },
            title = { Text("Confirm Deletion") },
            text = { Text("Are you sure you want to delete this poll? This action cannot be undone and will remove the poll and all its votes.") },
            confirmButton = {
                TextButton(onClick = { viewModel.handleEvent(ViewPrivatePollsEvent.ConfirmPollDeletion) }) {
                    Text("Delete", color = MaterialTheme.colorScheme.error)
                }
            },
            dismissButton = {
                TextButton(onClick = { viewModel.handleEvent(ViewPrivatePollsEvent.DismissPollDeletionDialog) }) {
                    Text("Cancel")
                }
            }
        )
    }

    // Handles the result passed back from PollDetailsScreen after a vote.
    val navBackStackEntry by navController.currentBackStackEntryAsState()
    LaunchedEffect(navBackStackEntry) {
        val votedPollId = navBackStackEntry?.savedStateHandle?.get<String>("votedPollId")
        if (votedPollId != null) {
            // Inform the ViewModel that this poll was voted on, so it can update its local state.
            viewModel.handleEvent(ViewPrivatePollsEvent.UserVotedOnPoll(votedPollId))
            // Clear the result from the SavedStateHandle to prevent re-processing on recomposition/back navigation.
            navBackStackEntry?.savedStateHandle?.remove<String>("votedPollId")
        }
    }

    Scaffold(
        snackbarHost = { SnackbarHost(hostState = snackbarHostState) },
        topBar = {
            CenterAlignedTopAppBar(
                title = { Text("View Private Polls") },
                navigationIcon = {
                    IconButton(onClick = onNavigateBack) {
                        Icon(
                            imageVector = Icons.AutoMirrored.Filled.ArrowBack,
                            contentDescription = "Navigate back"
                        )
                    }
                },
                colors = TopAppBarDefaults.centerAlignedTopAppBarColors(
                    containerColor = MaterialTheme.colorScheme.primary,
                    titleContentColor = MaterialTheme.colorScheme.onPrimary,
                    navigationIconContentColor = MaterialTheme.colorScheme.onPrimary
                )
            )
        }
    ) { paddingValues ->
        Column(
            modifier = Modifier
                .fillMaxSize()
                .padding(paddingValues)
        ) {
            when (val state = uiState) {
                is ViewPrivatePollsState.Loading -> {
                    Column(
                        modifier = Modifier.fillMaxSize().padding(16.dp),
                        horizontalAlignment = Alignment.CenterHorizontally,
                        verticalArrangement = Arrangement.Center
                    ) {
                        CircularProgressIndicator()
                        Text(text = "Loading polls...", modifier = Modifier.padding(top = 8.dp))
                    }
                }
                is ViewPrivatePollsState.Success -> {
                    if (state.polls.isEmpty()) {
                        Column(
                            modifier = Modifier.fillMaxSize().padding(16.dp),
                            horizontalAlignment = Alignment.CenterHorizontally,
                            verticalArrangement = Arrangement.Center
                        ) {
                             Text(text = "You haven't created or been invited to any private text polls yet.")
                        }
                    } else {
                        LazyColumn(
                            modifier = Modifier.fillMaxSize(),
                            contentPadding = PaddingValues(horizontal = 16.dp, vertical = 8.dp),
                            verticalArrangement = Arrangement.spacedBy(12.dp)
                        ) {
                            items(state.polls, key = { poll -> poll.pollId }) { pollUiModel ->
                                PollListItemCard(
                                    poll = pollUiModel,
                                    onPollClick = {
                                        viewModel.handleEvent(ViewPrivatePollsEvent.OnPollClick(pollUiModel.pollId))
                                    },
                                    onDeleteClick = {
                                        viewModel.handleEvent(ViewPrivatePollsEvent.OnDeletePollIconClick(pollUiModel.pollId))
                                    },
                                    currentUserId = viewModel.userRepository.getCurrentUser()?.uid ?: ""
                                )
                            }
                        }
                    }
                }
                is ViewPrivatePollsState.Empty -> {
                    Column(
                        modifier = Modifier.fillMaxSize().padding(16.dp),
                        horizontalAlignment = Alignment.CenterHorizontally,
                        verticalArrangement = Arrangement.Center
                    ) {
                        Text(text = "You haven't created or been invited to any private text polls yet.")
                    }
                }
                is ViewPrivatePollsState.Error -> {
                    Column(
                        modifier = Modifier.fillMaxSize().padding(16.dp),
                        horizontalAlignment = Alignment.CenterHorizontally,
                        verticalArrangement = Arrangement.Center
                    ) {
                        Text(text = "Error: ${state.message}", color = MaterialTheme.colorScheme.error)
                    }
                }
            }
        }
    }
}

// This is a private utility function to format a Firebase Timestamp into a human-readable string.
// It handles null timestamps and potential formatting errors gracefully.
private fun formatTimestamp(timestamp: Timestamp?): String {
    // If the timestamp is null, return "Not set" indicating that the date is not available.
    if (timestamp == null) return "Not set"
    return try {
        // Define the desired date and time format.
        // Example: "dd MMM yyyy, HH:mm" will produce something like "25 Dec 2023, 14:30".
        val sdf = SimpleDateFormat("dd MMM yyyy, HH:mm", Locale.getDefault())
        // Format the Timestamp (converted to a java.util.Date) into a string.
        sdf.format(timestamp.toDate())
    } catch (e: Exception) {
        // If any error occurs during formatting, return "Invalid date" to inform the user.
        "Invalid date"
    }
}

// This private utility function formats a poll type string (e.g., "TEXT_PRIVATE")
// into a more human-readable format (e.g., "Text Private Poll").
private fun formatPollType(pollType: String): String {
    return pollType
        .split('_')
        .joinToString(" ") { word ->
            word.lowercase(Locale.getDefault())
                .replaceFirstChar { if (it.isLowerCase()) it.titlecase(Locale.getDefault()) else it.toString() }
        } + " Poll"
}

// This composable function displays a single poll item in a card layout.
// It shows key information about the poll, such as its question, creator, status, and options.
// It also provides actions like clicking the poll to view details or deleting the poll (if the current user is the poll creator).
@OptIn(ExperimentalMaterial3Api::class)
@Composable
fun PollListItemCard(
    // The UI model containing all the data needed to display the poll item.
    poll: PollListItemUiModel,
    // Callback function invoked when the card is clicked, usually to navigate to poll details.
    // It passes the pollId of the clicked poll.
    onPollClick: (String) -> Unit,
    // Callback function invoked when the delete icon is clicked.
    // It passes the pollId of the poll to be deleted.
    onDeleteClick: (String) -> Unit,
    // The ID of the currently logged-in user, used to determine if the delete button should be shown (i.e., if the user is the poll creator).
    currentUserId: String
) {
    Card(
        modifier = Modifier
            .fillMaxWidth(),
            // .clickable { onPollClick(poll.pollId) }, // Click will be handled by the button
        shape = RoundedCornerShape(12.dp),
        elevation = CardDefaults.cardElevation(defaultElevation = 2.dp),
        colors = CardDefaults.cardColors(containerColor = MaterialTheme.colorScheme.surface)
    ) {
        Column(
            modifier = Modifier.padding(16.dp),
            verticalArrangement = Arrangement.spacedBy(4.dp) // Reduced space between items
        ) {
            Row(
                modifier = Modifier.fillMaxWidth(),
                verticalAlignment = Alignment.CenterVertically,
                horizontalArrangement = Arrangement.SpaceBetween
            ) {
                // This is item 3. Poll Question
                Text(
                    text = poll.question,
                    style = MaterialTheme.typography.titleMedium,
                    fontWeight = FontWeight.Bold,
                    maxLines = 2,
                    overflow = TextOverflow.Ellipsis,
                    modifier = Modifier.weight(1f)
                )
                if (poll.creatorId == currentUserId) {
                    IconButton(onClick = { onDeleteClick(poll.pollId) }, modifier = Modifier.size(24.dp)) {
                        Icon(
                            imageVector = Icons.Filled.Delete,
                            contentDescription = "Delete Poll",
                            tint = MaterialTheme.colorScheme.error
                        )
                    }
                }
            }

            // This is item 1. Created by (Avatar + Name)
            Row(
                verticalAlignment = Alignment.CenterVertically,
                modifier = Modifier.padding(top = 4.dp) // Add some top padding
            ) {
                Icon(
                    imageVector = Icons.Filled.Person,
                    contentDescription = "Creator",
                    modifier = Modifier.size(18.dp), // Consistent icon size
                    tint = MaterialTheme.colorScheme.primary // Changed to primary color
                )
                Spacer(modifier = Modifier.width(4.dp))
                Text(
                    text = "Created by: ", // Text split to place avatar correctly
                    style = MaterialTheme.typography.bodyMedium,
                    color = MaterialTheme.colorScheme.onSurfaceVariant
                )
                // Avatar Image
                AsyncImage(
                    model = poll.creatorAvatarUrl,
                    contentDescription = "Creator's avatar",
                    placeholder = painterResource(id = R.drawable.default_profile_pic), // Using your actual placeholder
                    error = painterResource(id = R.drawable.default_profile_pic), // Using your actual placeholder
                    modifier = Modifier
                        .size(24.dp)
                        .clip(CircleShape)
                )
                Spacer(modifier = Modifier.width(4.dp)) // Spacer between avatar and name
                Text(
                    text = poll.creatorName, // Display creator name after avatar
                    style = MaterialTheme.typography.bodyMedium,
                    color = MaterialTheme.colorScheme.onSurfaceVariant
                )
            }

            // 2. Poll Type: (e.g., "Text Poll")
            val anonymousStatus = if (poll.anonymous) " (Anonymous)" else " (Non-Anonymous)"
            InfoRow(icon = Icons.Filled.Poll, text = "Poll Type: ${formatPollType(poll.type)}$anonymousStatus")

            // 4. Number of Options
            PollInfoRow(icon = Icons.Filled.Person, text = poll.optionsSummary)

            // 5. Expires:
            PollInfoRow(icon = Icons.Filled.Timer, text = "Expires: ${formatTimestamp(poll.closesAt)}")

            // 6. Created:
            PollInfoRow(icon = Icons.Filled.Info, text = "Created: ${formatTimestamp(poll.createdAt)}")

            // 7. Votes 0: (You voted No) Leave this line as is
            val voteStatusText = if (poll.userHasVoted) {
                "Votes: ${poll.voteCount} (Voted)"
            } else {
                "Votes: ${poll.voteCount}"
            }
            PollInfoRow(
                icon = Icons.Filled.HowToVote,
                text = voteStatusText,
                highlight = poll.userHasVoted // Optionally highlight if user has voted
            )

            // Action Buttons: Vote/View Details and Delete (if applicable)
            Row(
                modifier = Modifier
                    .fillMaxWidth()
                    .padding(top = 8.dp),
                horizontalArrangement = Arrangement.End,
                verticalAlignment = Alignment.CenterVertically
            ) {
                // Vote/View Details Button
            Button(
                onClick = { onPollClick(poll.pollId) },
                    modifier = Modifier.padding(end = 8.dp),
                    colors = ButtonDefaults.buttonColors(
                        containerColor = MaterialTheme.colorScheme.primary,
                        contentColor = MaterialTheme.colorScheme.onPrimary
                    )
                ) {
                    Icon(
                        imageVector = Icons.Filled.HowToVote,
                        contentDescription = if (poll.userHasVoted) "View Poll Details" else "Vote on Poll",
                        modifier = Modifier.size(ButtonDefaults.IconSize)
                    )
                    Spacer(Modifier.size(ButtonDefaults.IconSpacing))
                    Text(if (poll.userHasVoted) "View Poll Details" else "Vote") // Conditional text
                }
            }
        }
    }
}

// Helper composable to display a row of information with an icon and text.
@Composable
private fun InfoRow(icon: androidx.compose.ui.graphics.vector.ImageVector, text: String, color: Color = Color.Unspecified) {
    Row(verticalAlignment = Alignment.CenterVertically) {
        Icon(
            imageVector = icon,
            contentDescription = null, // Decorative icon
            modifier = Modifier.size(18.dp), // Slightly smaller icon for info rows
            tint = if (color == Color.Unspecified) MaterialTheme.colorScheme.primary else color // Default to primary color for accent
        )
        Spacer(modifier = Modifier.width(8.dp))
        Text(text = text, style = MaterialTheme.typography.bodyMedium, color = if (color == Color.Unspecified) MaterialTheme.colorScheme.onSurfaceVariant else color) // Text color can remain onSurfaceVariant or be specified
    }
}

// Helper composable to display a row of information with an icon and text.
@Composable
private fun PollInfoRow(
    icon: androidx.compose.ui.graphics.vector.ImageVector,
    text: String,
    highlight: Boolean = false // Optional parameter to highlight the text
) {
    // Row layout for icon and text.
    Row(verticalAlignment = Alignment.CenterVertically, modifier = Modifier.padding(vertical = 2.dp)) {
        // Icon for the information row.
        Icon(
            imageVector = icon,
            contentDescription = null, // Decorative icon
            modifier = Modifier.size(18.dp), // Adjusted size
            tint = MaterialTheme.colorScheme.primary // Changed from onSurfaceVariant to primary
        )
        // Spacer between icon and text.
        Spacer(modifier = Modifier.width(8.dp))
        // Text content of the information row.
        Text(
            text = text,
            style = MaterialTheme.typography.bodyMedium, // Adjusted style
            color = if (highlight) MaterialTheme.colorScheme.primary else MaterialTheme.colorScheme.onSurface,
            fontWeight = if (highlight) FontWeight.Bold else FontWeight.Normal
        )
    }
}

@OptIn(ExperimentalMaterial3Api::class)
@Preview(showBackground = true, name = "Poll List Item Card")
@Composable
fun PollListItemCardPreview() {
    MaterialTheme {
        PollListItemCard(
            poll = PollListItemUiModel(
                pollId = "1",
                question = "What should we have for dinner tonight? This is a very long question to test ellipsis.",
                creatorName = "You",
                creatorId = "user123",
                creatorAvatarUrl = null,
                optionsSummary = "4 Options",
                numberOfOptions = 4,
                allowedVotersCount = 10,
                anonymous = false,
                type = "TEXT_PRIVATE",
                createdAt = Timestamp.now(),
                closesAt = null,
                userHasVoted = false,
                voteCount = 5
            ),
            onPollClick = {},
            onDeleteClick = {},
            currentUserId = "user123"
        )
    }
}

@OptIn(ExperimentalMaterial3Api::class)
@Preview(showBackground = true, name = "View Private Polls - Empty")
@Composable
fun ViewPrivatePollsScreenEmptyPreview() {
    MaterialTheme {
        Scaffold(
            topBar = {
                CenterAlignedTopAppBar(title = { Text("View Private Polls") })
            }
        ) { paddingValues ->
            Column(
                modifier = Modifier.fillMaxSize().padding(paddingValues).padding(16.dp),
                horizontalAlignment = Alignment.CenterHorizontally,
                verticalArrangement = Arrangement.Center
            ) {
                Text(text = "You haven't created or been invited to any private text polls yet.")
            }
        }
    }
}

@OptIn(ExperimentalMaterial3Api::class)
@Preview(showBackground = true, name = "View Private Polls - Loading")
@Composable
fun ViewPrivatePollsScreenLoadingPreview() {
    MaterialTheme {
        Scaffold(
            topBar = {
                CenterAlignedTopAppBar(title = { Text("View Private Polls") })
            }
        ) { paddingValues ->
            Column(
                modifier = Modifier.fillMaxSize().padding(paddingValues).padding(16.dp),
                horizontalAlignment = Alignment.CenterHorizontally,
                verticalArrangement = Arrangement.Center
            ) {
                CircularProgressIndicator()
                Text(text = "Loading polls...", modifier = Modifier.padding(top = 8.dp))
            }
        }
    }
} 