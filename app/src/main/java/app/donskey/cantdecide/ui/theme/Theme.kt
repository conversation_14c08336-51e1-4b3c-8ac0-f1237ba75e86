package app.donskey.cantdecide.ui.theme

import android.app.Activity
import android.os.Build
import androidx.compose.material3.MaterialTheme
import androidx.compose.material3.darkColorScheme
import androidx.compose.material3.dynamicDarkColorScheme
import androidx.compose.material3.dynamicLightColorScheme
import androidx.compose.material3.lightColorScheme
import androidx.compose.runtime.Composable
import androidx.compose.runtime.SideEffect
import androidx.compose.ui.graphics.Color
import androidx.compose.ui.platform.LocalContext
import androidx.compose.ui.platform.LocalView
import androidx.core.view.WindowCompat

/**
 * Composable function to apply the application theme.
 * It sets up the color scheme (light/dark, dynamic colors) and status bar appearance.
 * Uses ThemeManager to determine theme settings and customizes the appearance 
 * based on user preferences.
 *
 * @param darkTheme Whether the theme should be dark (defaults to ThemeManager setting).
 * @param dynamicColor Whether to use dynamic colors (available on Android 12+, defaults to false).
 * @param content The composable content to which the theme will be applied.
 */
@Composable
fun AppTheme(
    darkTheme: Boolean = ThemeManager.isDarkMode.value,
    // Dynamic color is available on Android 12+
    dynamicColor: Boolean = false, // Set to false to use our custom accent colors
    content: @Composable () -> Unit
) {
    // Get the current accent color from ThemeManager
    val accentColor = ThemeManager.currentAccentColor
    
    // Create custom color schemes with our accent color - lighter dark theme
    val customDarkColorScheme = darkColorScheme(
        primary = accentColor,
        secondary = accentColor.copy(alpha = 0.8f),
        tertiary = accentColor.copy(alpha = 0.6f),
        // Much lighter dark theme colors
        background = Color(0xFF2D2D2D), // Much lighter dark background
        surface = Color(0xFF353535), // Much lighter dark surface
        surfaceContainerLow = Color(0xFF404040), // Lighter container
        surfaceVariant = Color(0xFF404040), // Lighter variant
        surfaceContainer = Color(0xFF383838), // Lighter container
        onBackground = Color(0xFFE8E8E8), // Softer white for better contrast
        onSurface = Color(0xFFE8E8E8), // Softer white for better contrast
        outline = Color(0xFF9E9E9E) // Lighter outline
    )
    
    // Create a custom light color scheme that incorporates custom accent color
    val customLightColorScheme = lightColorScheme(
        primary = accentColor,
        secondary = accentColor.copy(alpha = 0.8f),
        tertiary = accentColor.copy(alpha = 0.6f)
    )
    
    // Determine the color scheme based on dark theme and dynamic color settings
    val colorScheme = when {
        dynamicColor && Build.VERSION.SDK_INT >= Build.VERSION_CODES.S -> {
            val context = LocalContext.current
            if (darkTheme) dynamicDarkColorScheme(context) else dynamicLightColorScheme(context)
        }
        darkTheme -> customDarkColorScheme
        else -> customLightColorScheme
    }
    
    // Set status bar appearance based on the theme
    val view = LocalView.current
    if (!view.isInEditMode) {
        SideEffect {
            val window = (view.context as Activity).window
            // Enable edge-to-edge. This should inherently make system bars transparent
            // or allow content to draw behind them.
            WindowCompat.setDecorFitsSystemWindows(window, false)

            // REMOVE direct color manipulation when setDecorFitsSystemWindows is false
            // window.statusBarColor = Color.Transparent.toArgb()
            // window.navigationBarColor = Color.Transparent.toArgb()

            // Set appearance of status bar icons
            WindowCompat.getInsetsController(window, view).isAppearanceLightStatusBars = !darkTheme
            // Set appearance of navigation bar icons
            WindowCompat.getInsetsController(window, view).isAppearanceLightNavigationBars = !darkTheme
        }
    }

    // Apply the MaterialTheme with the selected color scheme and typography
    MaterialTheme(
        colorScheme = colorScheme,
        typography = Typography,
        content = content
    )
} 