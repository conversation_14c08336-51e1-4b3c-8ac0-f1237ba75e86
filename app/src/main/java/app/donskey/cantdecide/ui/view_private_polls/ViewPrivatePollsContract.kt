package app.donskey.cantdecide.ui.view_private_polls

import app.donskey.cantdecide.data.model.PollOption // Changed from com.donskey to app.donskey
import com.google.firebase.Timestamp

// This data class will hold the information needed to display a single poll item in the list.
data class PollListItemUiModel(
    val pollId: String,
    val question: String,
    val creatorName: String,
    val creatorId: String,
    val creatorAvatarUrl: String?, // Added for creator's avatar
    val optionsSummary: String, // e.g., "3 Options"
    val numberOfOptions: Int,
    val allowedVotersCount: Int,
    val anonymous: Boolean,
    val type: String, // e.g., "TEXT_PRIVATE"
    val createdAt: Timestamp,
    val closesAt: Timestamp?,
    val userHasVoted: Boolean, // To indicate if the current user has already voted on this poll
    val voteCount: Int // Total votes cast on this poll
)

// Defines the state of the ViewPrivatePollsScreen.
sealed interface ViewPrivatePollsState {
    // Represents the initial or loading state.
    data object Loading : ViewPrivatePollsState

    // Represents the state when polls are successfully loaded.
    data class Success(val polls: List<PollListItemUiModel>) : ViewPrivatePollsState

    // Represents the state when no polls are found.
    data object Empty : ViewPrivatePollsState

    // Represents an error state.
    data class Error(val message: String) : ViewPrivatePollsState
}

// Defines the events that can be triggered from the UI.
sealed interface ViewPrivatePollsEvent {
    // Event triggered when the screen is first loaded or needs a refresh.
    data object LoadPrivatePolls : ViewPrivatePollsEvent

    // Event triggered when a poll item is clicked.
    data class OnPollClick(val pollId: String) : ViewPrivatePollsEvent

    // Event triggered when a delete action is initiated for a poll (by its creator).
    data class OnDeletePollIconClick(val pollId: String) : ViewPrivatePollsEvent

    // Event triggered when the user votes on a poll.
    data class UserVotedOnPoll(val pollId: String) : ViewPrivatePollsEvent

    // Event triggered when user confirms deletion in the dialog.
    data object ConfirmPollDeletion : ViewPrivatePollsEvent

    // Event triggered when user dismisses the deletion dialog.
    data object DismissPollDeletionDialog : ViewPrivatePollsEvent
}

// Defines one-time effects that can be triggered from the ViewModel (e.g., navigation, showing toasts).
sealed interface ViewPrivatePollsEffect {
    // Effect to navigate to the poll details screen.
    data class NavigateToPollDetails(val pollId: String) : ViewPrivatePollsEffect

    // Effect to show a generic message (e.g., toast or snackbar).
    data class ShowMessage(val message: String) : ViewPrivatePollsEffect
    
    // ConfirmPollDeletion effect is removed as dialog is driven by state now.
} 