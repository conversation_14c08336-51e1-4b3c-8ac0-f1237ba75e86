package app.donskey.cantdecide.ui.auth

import android.content.Intent
import android.os.Bundle
import android.util.Log
import androidx.appcompat.app.AppCompatActivity
import androidx.activity.compose.setContent
import androidx.compose.runtime.getValue
import androidx.compose.runtime.mutableStateOf
import androidx.compose.runtime.setValue
import androidx.core.view.WindowCompat
import androidx.lifecycle.lifecycleScope
import com.google.firebase.FirebaseException
import com.google.firebase.auth.FirebaseAuth
import com.google.firebase.auth.PhoneAuthCredential
import com.google.firebase.auth.PhoneAuthOptions
import com.google.firebase.auth.PhoneAuthProvider
import kotlinx.coroutines.launch
import java.util.concurrent.TimeUnit
import androidx.compose.ui.platform.LocalSoftwareKeyboardController
import androidx.compose.ui.platform.SoftwareKeyboardController
import app.donskey.cantdecide.MainActivity
import app.donskey.cantdecide.data.repositories.UserRepository
import app.donskey.cantdecide.firebase.FirebaseHelper
import app.donskey.cantdecide.ui.theme.AppTheme
import app.donskey.cantdecide.util.PhoneNumberUtil
import dagger.hilt.android.AndroidEntryPoint
import javax.inject.Inject

/**
 * Login activity for handling phone authentication.
 * Users enter their phone number here to receive a verification code.
 */
@AndroidEntryPoint
class LoginActivity : AppCompatActivity() {
    
    companion object {
        private const val TAG = "LoginActivity"
        private const val VERIFICATION_TIMEOUT = 60L // seconds
    }
    
    @Inject lateinit var auth: FirebaseAuth
    @Inject lateinit var userRepository: UserRepository
    
    // Store verification ID for later use
    private var storedVerificationId: String? = null
    // Store the number used for verification to pass to next activity
    private var numberSentForVerification: String? = null
    private var resendToken: PhoneAuthProvider.ForceResendingToken? = null
    
    // UI state
    private var isLoading by mutableStateOf(false)
    private var errorMessage by mutableStateOf<String?>(null)
    
    // If LoginViewModel is a @HiltViewModel, it will get its dependencies injected automatically.
    // No need to manually pass UserRepository to it if UserRepository is injected into LoginViewModel.
    // Assuming LoginViewModel handles its own UserRepository dependency via Hilt:
    // private val loginViewModel: LoginViewModel by viewModels()
    
    // If LoginActivity *itself* directly needed UserRepository (less common, usually VM handles it):
    // @Inject lateinit var userRepository: UserRepository 
    
    override fun onCreate(savedInstanceState: Bundle?) {
        super.onCreate(savedInstanceState)
        
        // Configure window to handle insets properly for better keyboard handling
        WindowCompat.setDecorFitsSystemWindows(window, false)
        
        // Check if user is already logged in
        if (userRepository.isUserLoggedIn()) {
            // User is logged in, navigate to MainActivity
            // Consider adding profile completion check here in the future
            Log.d(TAG, "User already logged in, navigating to MainActivity.")
            navigateToMain()
            return // Important to return to prevent further execution of onCreate
        }
        
        setContent {
            AppTheme {
                // Get keyboard controller here to pass down
                val keyboardController = LocalSoftwareKeyboardController.current

                LoginScreen(
                    onPhoneNumberSubmit = { phoneNumber, countryCode ->
                        validateAndSendCode(phoneNumber, countryCode, keyboardController)
                    },
                    isLoading = isLoading,
                    errorMessage = errorMessage
                )
            }
        }
        
        // Log analytics event
        FirebaseHelper.logEvent("login_screen_opened")
    }
    
    /**
     * Validates the phone number input and initiates the verification process.
     * Hides the keyboard immediately.
     * @param phoneNumber Raw phone number input
     * @param countryCode Selected country code
     * @param keyboardController Controller to hide the keyboard
     */
    private fun validateAndSendCode(phoneNumber: String, countryCode: String, keyboardController: SoftwareKeyboardController?) {
        errorMessage = null // Clear previous error
        val fullPhoneNumber = "+$countryCode$phoneNumber"
        Log.d(TAG, "Validating phone number: $fullPhoneNumber")
        numberSentForVerification = fullPhoneNumber // Store the number being verified

        if (!PhoneNumberUtil.isValidPhoneNumber(fullPhoneNumber)) {
            errorMessage = "Invalid phone number format."
            Log.w(TAG, "Invalid phone number format: $fullPhoneNumber")
            numberSentForVerification = null // Clear if invalid
            return
        }
        
        isLoading = true
        keyboardController?.hide()

        val options = PhoneAuthOptions.newBuilder(this.auth) // Use injected auth
            .setPhoneNumber(fullPhoneNumber) 
            .setTimeout(VERIFICATION_TIMEOUT, TimeUnit.SECONDS) 
            .setActivity(this) 
            .setCallbacks(callbacks) 
            .build()
        PhoneAuthProvider.verifyPhoneNumber(options)
        Log.d(TAG, "Verification code sent to $fullPhoneNumber")
    }
    
    /**
     * Callbacks for phone authentication.
     */
    private val callbacks = object : PhoneAuthProvider.OnVerificationStateChangedCallbacks() {
        
        override fun onVerificationCompleted(credential: PhoneAuthCredential) {
            // This will be called if verification is completed automatically (rare on most devices)
            Log.d(TAG, "onVerificationCompleted: auto-retrieval or instant verification.")
            
            isLoading = false
            signInWithPhoneAuthCredential(credential)
        }
        
        override fun onVerificationFailed(e: FirebaseException) {
            Log.e(TAG, "onVerificationFailed", e)
            
            isLoading = false
            // Set error message from exception
            errorMessage = "Verification failed: ${e.localizedMessage}"
            
            // Log analytics event
            FirebaseHelper.logEvent("login_verification_failed", Bundle().apply {
                putString("error_message", e.message)
                putString("error_code", if (e is com.google.firebase.auth.FirebaseAuthException) e.errorCode else "UNKNOWN")
            })
        }
        
        override fun onCodeSent(
            verificationId: String,
            token: PhoneAuthProvider.ForceResendingToken
        ) {
            // Code has been sent to the user
            Log.d(TAG, "onCodeSent:SMS sent. Verification ID: $verificationId")
            
            // Save verification ID
            storedVerificationId = verificationId
            <EMAIL> = token
            
            // Hide loading
            isLoading = false
            
            // Log analytics event
            FirebaseHelper.logEvent("verification_code_sent")
            
            // Navigate to verification activity
            navigateToVerification(verificationId)
        }
    }
    
    /**
     * Signs in the user with a phone auth credential.
     * @param credential PhoneAuthCredential The credential to sign in with
     */
    private fun signInWithPhoneAuthCredential(credential: PhoneAuthCredential) {
        isLoading = true
        this.auth.signInWithCredential(credential) // Use injected auth
            .addOnCompleteListener(this) { task ->
                if (task.isSuccessful) {
                    val firebaseUser = task.result?.user
                    if (firebaseUser != null) {
                        Log.d(TAG, "signInWithPhoneAuthCredential successful. User: ${firebaseUser.uid}")
                        // Create user in Firestore if they don't exist
                        lifecycleScope.launch { // Need lifecycleScope or other coroutine scope
                            val success = userRepository.createUserIfNotExists(firebaseUser.phoneNumber ?: "") // Use injected userRepository
                            if (success) {
                                Log.d(TAG, "User profile ensured in Firestore. Navigating to Main.")
                                FirebaseHelper.logEvent("login_successful")
                                navigateToMainActivity()
                            } else {
                                isLoading = false
                                errorMessage = "Failed to set up user profile. Please try again."
                                Log.e(TAG, "Failed to create or verify user in Firestore.")
                                FirebaseHelper.logEvent("login_profile_setup_failed")
                            }
                        }
                    } else {
                        isLoading = false
                        errorMessage = "Authentication successful but no user data found."
                        Log.e(TAG, "signInWithPhoneAuthCredential: User is null after successful sign-in.")
                        FirebaseHelper.logEvent("login_user_null_after_success")
                    }
                } else {
                    isLoading = false
                    errorMessage = "Sign-in failed: ${task.exception?.localizedMessage}"
                    Log.e(TAG, "signInWithPhoneAuthCredential failed", task.exception)
                     FirebaseHelper.logEvent("login_signin_failed", Bundle().apply {
                        putString("error_message", task.exception?.message)
                    })
                }
            }
    }
    
    /**
     * Navigates to the MainActivity and finishes this activity.
     */
    private fun navigateToMainActivity() {
        val intent = Intent(this, MainActivity::class.java)
        intent.flags = Intent.FLAG_ACTIVITY_NEW_TASK or Intent.FLAG_ACTIVITY_CLEAR_TASK
        startActivity(intent)
        finish()
    }
    
    private fun navigateToMain() {
        val intent = Intent(this, MainActivity::class.java)
        intent.flags = Intent.FLAG_ACTIVITY_NEW_TASK or Intent.FLAG_ACTIVITY_CLEAR_TASK
        startActivity(intent)
        finish()
    }
    
    private fun navigateToVerification(verificationId: String) {
        val intent = Intent(this@LoginActivity, VerificationActivity::class.java).apply {
            putExtra(VerificationActivity.EXTRA_VERIFICATION_ID, verificationId)
            putExtra(VerificationActivity.EXTRA_PHONE_NUMBER, numberSentForVerification ?: "")
        }
        startActivity(intent)
        Log.d(TAG, "Navigating to VerificationActivity with ID: $verificationId")
    }
} 