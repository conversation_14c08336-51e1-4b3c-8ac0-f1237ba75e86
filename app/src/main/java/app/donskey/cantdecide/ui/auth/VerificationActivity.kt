package app.donskey.cantdecide.ui.auth

import android.content.Intent
import android.os.Bundle
import android.os.CountDownTimer
import android.util.Log
import androidx.activity.compose.setContent
import androidx.appcompat.app.AppCompatActivity
import androidx.compose.runtime.LaunchedEffect
import androidx.compose.runtime.getValue
import androidx.compose.runtime.mutableStateOf
import androidx.compose.runtime.setValue
import androidx.compose.runtime.mutableIntStateOf
import androidx.core.view.WindowCompat
import androidx.lifecycle.lifecycleScope
import com.google.firebase.FirebaseException
import com.google.firebase.auth.FirebaseAuth
import com.google.firebase.auth.PhoneAuthCredential
import com.google.firebase.auth.PhoneAuthOptions
import com.google.firebase.auth.PhoneAuthProvider
import kotlinx.coroutines.launch
import kotlinx.coroutines.tasks.await
import androidx.compose.ui.platform.LocalSoftwareKeyboardController
import androidx.compose.ui.platform.SoftwareKeyboardController
import app.donskey.cantdecide.MainActivity
import app.donskey.cantdecide.R
import app.donskey.cantdecide.data.repositories.UserRepository
import app.donskey.cantdecide.firebase.FirebaseHelper
import app.donskey.cantdecide.ui.theme.AppTheme
import app.donskey.cantdecide.ui.theme.ThemeManager
import app.donskey.cantdecide.util.PhoneNumberUtil
import com.google.firebase.messaging.FirebaseMessaging
import dagger.hilt.android.AndroidEntryPoint
import javax.inject.Inject
import java.util.concurrent.TimeUnit

/**
 * Verification activity for validating phone numbers.
 * Users enter the verification code they received via SMS here.
 */
@AndroidEntryPoint
class VerificationActivity : AppCompatActivity() {
    
    @Inject lateinit var auth: FirebaseAuth
    @Inject lateinit var userRepository: UserRepository
    
    companion object {
        private const val TAG = "VerificationActivity"
        private const val RESEND_TIMEOUT = 60 // seconds
        
        // Intent extras
        const val EXTRA_VERIFICATION_ID = "verification_id"
        const val EXTRA_PHONE_NUMBER = "phone_number"
        const val EXTRA_RAW_PHONE_NUMBER = "raw_phone_number"
        const val EXTRA_COUNTRY_CODE = "country_code"
    }
    
    private var verificationId: String? = null
    private var phoneNumber: String? = null
    private var rawPhoneNumber: String? = null
    private var countryCode: String? = null
    
    private var resendToken: PhoneAuthProvider.ForceResendingToken? = null
    private var resendTimeRemaining by mutableIntStateOf(RESEND_TIMEOUT)
    private var resendTimer: CountDownTimer? = null
    
    // UI state
    private var isLoading by mutableStateOf(false)
    private var errorMessage by mutableStateOf<String?>(null)
    
    override fun onCreate(savedInstanceState: Bundle?) {
        super.onCreate(savedInstanceState)
        
        // Configure window to handle insets properly for better keyboard handling
        WindowCompat.setDecorFitsSystemWindows(window, false)
        
        // Get verification ID from intent
        verificationId = intent.getStringExtra(EXTRA_VERIFICATION_ID)
        phoneNumber = intent.getStringExtra(EXTRA_PHONE_NUMBER)
        rawPhoneNumber = intent.getStringExtra(EXTRA_RAW_PHONE_NUMBER)
        countryCode = intent.getStringExtra(EXTRA_COUNTRY_CODE)
        
        if (verificationId.isNullOrEmpty()) {
            Log.e(TAG, "No verification ID provided")
            finish()
            return
        }
        
        // Set up the UI
        setContent {
            AppTheme {
                // Get keyboard controller
                val keyboardController = LocalSoftwareKeyboardController.current

                VerificationScreen(
                    phoneNumber = phoneNumber ?: "",
                    onVerifyClick = { code -> verifyCode(code, keyboardController) },
                    onResendClick = { resendVerificationCode() },
                    onChangeNumberClick = { onChangeNumberClick() },
                    isLoading = isLoading,
                    errorMessage = errorMessage,
                    resendTimeRemaining = resendTimeRemaining
                )
                
                // Start the timer when the screen is first shown
                LaunchedEffect(Unit) {
                    startResendTimer()
                }
            }
        }
        
        // Log analytics event
        FirebaseHelper.logEvent("verification_screen_opened")
    }
    
    /**
     * Handles verification code entry and validation.
     * @param code The verification code entered by the user
     * @param keyboardController The keyboard controller to hide the keyboard
     */
    private fun verifyCode(code: String, keyboardController: SoftwareKeyboardController?) {
        // Hide keyboard immediately
        keyboardController?.hide()

        if (code.length != 6) {
            errorMessage = getString(R.string.error_verifying_code)
            return
        }
        
        // Show loading state
        isLoading = true
        errorMessage = null
        
        val credential = PhoneAuthProvider.getCredential(verificationId ?: "", code)
        signInWithPhoneAuthCredential(credential)
    }
    
    /**
     * Handles the "Change Number" button click.
     * Returns to the login screen.
     */
    private fun onChangeNumberClick() {
        finish()
    }
    
    /**
     * Resends the verification code to the user's phone.
     */
    private fun resendVerificationCode() {
        if (resendTimeRemaining > 0) {
            return // Still waiting for resend timer
        }
        
        if (rawPhoneNumber.isNullOrEmpty() || countryCode.isNullOrEmpty()) {
            errorMessage = getString(R.string.error_generic)
            return
        }
        
        // Show loading state
        isLoading = true
        errorMessage = null
        
        // Format the phone number for resend
        val formattedPhoneNumber = PhoneNumberUtil.formatPhoneNumberE164(
            rawPhoneNumber ?: "", 
            countryCode ?: ""
        )
        
        if (formattedPhoneNumber.isBlank()) {
            errorMessage = getString(R.string.error_invalid_phone)
            isLoading = false
            return
        }
        
        // Create verification options with resend token if available
        val optionsBuilder = PhoneAuthOptions.newBuilder(auth)
            .setPhoneNumber(formattedPhoneNumber)
            .setTimeout(RESEND_TIMEOUT.toLong(), TimeUnit.SECONDS)
            .setActivity(this)
            .setCallbacks(phoneAuthCallbacks)
        
        // Add resend token if available
        resendToken?.let { token ->
            optionsBuilder.setForceResendingToken(token)
        }
        
        // Start verification
        PhoneAuthProvider.verifyPhoneNumber(optionsBuilder.build())
        
        // Log analytics event
        FirebaseHelper.logEvent("verification_code_resend")
    }
    
    /**
     * Starts the resend timer.
     */
    private fun startResendTimer() {
        // Cancel any existing timer
        resendTimer?.cancel()
        
        // Reset time remaining
        resendTimeRemaining = RESEND_TIMEOUT
        
        // Create and start a new timer
        resendTimer = object : CountDownTimer(RESEND_TIMEOUT * 1000L, 1000) {
            override fun onTick(millisUntilFinished: Long) {
                resendTimeRemaining = (millisUntilFinished / 1000).toInt()
            }
            
            override fun onFinish() {
                resendTimeRemaining = 0
            }
        }.start()
    }
    
    /**
     * Phone auth callbacks for handling verification events.
     */
    private val phoneAuthCallbacks = object : PhoneAuthProvider.OnVerificationStateChangedCallbacks() {
        
        override fun onVerificationCompleted(credential: PhoneAuthCredential) {
            // This will be called if verification is completed automatically
            Log.d(TAG, "onVerificationCompleted")
            
            isLoading = false
            signInWithPhoneAuthCredential(credential)
        }
        
        override fun onVerificationFailed(e: FirebaseException) {
            Log.e(TAG, "onVerificationFailed: ${e.message}", e)
            
            isLoading = false
            errorMessage = getString(R.string.error_sending_code)
            
            // Log analytics event
            FirebaseHelper.logEvent("verification_failed")
        }
        
        override fun onCodeSent(
            verificationId: String,
            token: PhoneAuthProvider.ForceResendingToken
        ) {
            // Code has been sent to the user
            Log.d(TAG, "onCodeSent: $verificationId")
            
            // Save verification ID and token
            <EMAIL> = verificationId
            <EMAIL> = token
            
            // Hide loading and reset error
            isLoading = false
            errorMessage = null
            
            // Restart the resend timer
            startResendTimer()
            
            // Log analytics event
            FirebaseHelper.logEvent("verification_code_resent")
        }
    }
    
    /**
     * Signs the user in with the provided credential.
     * Fetches profile, applies theme, and navigates on success.
     * @param credential The phone auth credential
     */
    private fun signInWithPhoneAuthCredential(credential: PhoneAuthCredential) {
        // Keep isLoading true until navigation or error
        isLoading = true
        errorMessage = null

        lifecycleScope.launch { // Use coroutine for async operations
            try {
                // Sign in with credential
                val authResult = auth.signInWithCredential(credential).await()
                val user = authResult.user
                
                if (user != null) {
                    Log.d(TAG, "signInWithCredential:success ${user.uid}")

                    // Create user in Firestore if needed (assuming it handles new/existing)
                    val phoneNumberForFirestore = phoneNumber ?: user.phoneNumber ?: ""
                    val userCreatedOrExists = userRepository.createUserIfNotExists(phoneNumberForFirestore)

                    if (userCreatedOrExists) {
                        // --- ADD FCM TOKEN UPDATE HERE ---
                        try {
                            Log.d(TAG, "Attempting to fetch and save FCM token for user ${user.uid}")
                            val fcmToken = FirebaseMessaging.getInstance().token.await()
                            if (fcmToken.isNotBlank()) {
                                userRepository.updateFcmToken(user.uid, fcmToken) // No need for await if updateFcmToken is suspend
                                Log.d(TAG, "FCM token fetched and update initiated for user ${user.uid}")
                            } else {
                                Log.w(TAG, "Fetched FCM token is blank for user ${user.uid}")
                            }
                        } catch (e: Exception) {
                            Log.e(TAG, "Error fetching or saving FCM token for user ${user.uid}", e)
                            // Non-fatal, continue with login flow
                        }
                        // --- END FCM TOKEN UPDATE ---

                        // Fetch user profile AND settings AFTER successful login/creation
                        Log.d(TAG, "Fetching user profile for theme...")
                        val userProfile = userRepository.getUserProfile()

                        if (userProfile != null) {
                            // Apply the fetched theme NOW
                            Log.d(TAG, "Applying user theme: Dark=${userProfile.themePreferences.darkMode}, ColorIndex=${userProfile.themePreferences.themeColorIndex}")
                            ThemeManager.initializeFromPreferences(
                                isDark = userProfile.themePreferences.darkMode,
                                colorIndex = userProfile.themePreferences.themeColorIndex
                            )

                            // Check profile completion AFTER fetching profile
                            if (userRepository.hasCompletedProfile()) { // Use await() if hasCompletedProfile is suspend
                                // Navigate to main activity only AFTER theme is applied
                                Log.d(TAG, "Profile complete, navigating to MainActivity.")
                                navigateToMainActivity()
                            } else {
                                // Navigate to profile setup (adjust if needed)
                                Log.d(TAG, "Profile incomplete, navigating to MainActivity (assuming setup happens there or needs adjustment).")
                                navigateToMainActivity() // TODO: Change to ProfileSetupActivity if it exists
                            }
                        } else {
                            Log.e(TAG, "Failed to fetch user profile after login/creation.")
                            errorMessage = getString(R.string.error_generic) // Show error on verification screen
                            isLoading = false // Stop loading as we are not navigating
                        }
                    } else {
                        Log.e(TAG, "Failed to create or find user in Firestore.")
                        errorMessage = getString(R.string.error_generic) // Show error on verification screen
                        isLoading = false // Stop loading
                    }
                } else {
                     Log.e(TAG, "signInWithCredential:failure - User is null after successful sign-in task.")
                     errorMessage = getString(R.string.error_verifying_code)
                     isLoading = false // Stop loading
                }

            } catch (e: Exception) {
                Log.e(TAG, "signInWithCredential:failure", e)
                errorMessage = getString(R.string.error_verifying_code) + ": " + e.localizedMessage
                // Log analytics event
                FirebaseHelper.logEvent("login_failed")
                isLoading = false // Stop loading on error
            }
            // isLoading might remain true if navigation occurs successfully,
            // as the activity will finish. Setting false here ensures it stops if navigation fails.
            // If navigation succeeds, this line might not be reached if finish() is called immediately.
            if (isLoading) isLoading = false
        }
    }
    
    /**
     * Navigates to the MainActivity and finishes this activity.
     */
    private fun navigateToMainActivity() {
        val intent = Intent(this, MainActivity::class.java)
        // Clear the back stack so user can't go back to verification
        intent.flags = Intent.FLAG_ACTIVITY_NEW_TASK or Intent.FLAG_ACTIVITY_CLEAR_TASK
        startActivity(intent)
        finish() // Close this activity
    }
    
    override fun onDestroy() {
        super.onDestroy()
        // Cancel timer when activity is destroyed
        resendTimer?.cancel()
    }
} 