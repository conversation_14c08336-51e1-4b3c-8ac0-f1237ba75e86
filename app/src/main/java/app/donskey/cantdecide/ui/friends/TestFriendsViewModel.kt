package app.donskey.cantdecide.ui.friends

import androidx.lifecycle.ViewModel
import app.donskey.cantdecide.data.repositories.UserRepository
import dagger.hilt.android.lifecycle.HiltViewModel
import javax.inject.Inject
import android.util.Log

@HiltViewModel
class TestFriendsViewModel 
@Inject constructor(
    private val userRepository: UserRepository 
) : ViewModel() {
    init {
        Log.d("HiltTest", "TestFriendsViewModel initialized with UserRepository: $userRepository")
    }
    // You can add a simple public val for the UI to observe if needed for testing
    val testMessage: String = "TestFriendsViewModel Loaded Successfully"
} 