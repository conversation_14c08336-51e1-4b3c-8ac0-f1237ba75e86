package app.donskey.cantdecide.ui.view_private_polls

import android.util.Log
import androidx.lifecycle.ViewModel
import androidx.lifecycle.viewModelScope
import app.donskey.cantdecide.data.model.PollVote
import app.donskey.cantdecide.data.model.PrivateTextPoll
import app.donskey.cantdecide.data.repositories.UserRepository
import app.donskey.cantdecide.domain.repository.PollsRepository
import app.donskey.cantdecide.ui.view_private_polls.ViewPrivatePollsState
import app.donskey.cantdecide.ui.view_private_polls.ViewPrivatePollsEffect
import app.donskey.cantdecide.ui.view_private_polls.ViewPrivatePollsEvent
import app.donskey.cantdecide.ui.view_private_polls.PollListItemUiModel
import com.google.firebase.Timestamp
import dagger.hilt.android.lifecycle.HiltViewModel
import kotlinx.coroutines.flow.Flow
import kotlinx.coroutines.flow.MutableSharedFlow
import kotlinx.coroutines.flow.MutableStateFlow
import kotlinx.coroutines.flow.SharedFlow
import kotlinx.coroutines.flow.StateFlow
import kotlinx.coroutines.flow.asSharedFlow
import kotlinx.coroutines.flow.asStateFlow
import kotlinx.coroutines.flow.catch
import kotlinx.coroutines.flow.combine
import kotlinx.coroutines.launch
import javax.inject.Inject

// This ViewModel is responsible for managing the state and logic for the ViewPrivatePollsScreen.
// It fetches private polls created by the user and polls they are invited to,
// combines them, and transforms them into a displayable format.
@HiltViewModel
class ViewPrivatePollsViewModel @Inject constructor(
    // Repository to fetch poll data.
    private val pollsRepository: PollsRepository,
    // Repository to fetch user-related data, like the current user ID.
    val userRepository: UserRepository
) : ViewModel() {

    // _uiState holds the current state of the screen (e.g., Loading, Success, Error).
    // It's a MutableStateFlow, meaning its value can be updated, and it emits the new value to its collectors.
    private val _uiState: MutableStateFlow<ViewPrivatePollsState> =
        MutableStateFlow(ViewPrivatePollsState.Loading)
    // uiState is the public, read-only version of _uiState, exposed as a StateFlow.
    // Composables in the UI can collect this flow to react to state changes.
    val uiState: StateFlow<ViewPrivatePollsState> = _uiState.asStateFlow()

    // _pollIdPendingDeletion holds the ID of the poll that the user has tapped to delete,
    // triggering the confirmation dialog. Null if no deletion is pending.
    private val _pollIdPendingDeletion: MutableStateFlow<String?> = MutableStateFlow(null)
    val pollIdPendingDeletion: StateFlow<String?> = _pollIdPendingDeletion.asStateFlow()

    // _uiEffect is used to send one-time events from the ViewModel to the UI (e.g., navigation, showing a toast).
    // It's a MutableSharedFlow, suitable for events that should be consumed only once.
    private val _uiEffect: MutableSharedFlow<ViewPrivatePollsEffect> = MutableSharedFlow()
    // uiEffect is the public, read-only version of _uiEffect, exposed as a SharedFlow.
    val uiEffect: SharedFlow<ViewPrivatePollsEffect> = _uiEffect.asSharedFlow()

    // Companion object for constants, like the TAG for logging.
    companion object {
        private const val TAG = "ViewPrivatePollsVM"
    }

    // The init block is executed when the ViewModel is created.
    // Here, it triggers the initial loading of polls.
    init {
        handleEvent(ViewPrivatePollsEvent.LoadPrivatePolls)
    }

    // This function processes events sent from the UI.
    // It uses a ViewModelScope.launch to run suspend functions in a coroutine tied to the ViewModel's lifecycle.
    fun handleEvent(event: ViewPrivatePollsEvent) {
        viewModelScope.launch {
            when (event) {
                // When LoadPrivatePolls event is received, call the loadPolls function.
                is ViewPrivatePollsEvent.LoadPrivatePolls -> {
                    loadPolls()
                }
                // When OnPollClick event is received, emit a NavigateToPollDetails effect.
                is ViewPrivatePollsEvent.OnPollClick -> {
                    _uiEffect.emit(ViewPrivatePollsEffect.NavigateToPollDetails(event.pollId))
                }
                // When OnDeletePollClick event is received, set the pollIdPendingDeletion state.
                is ViewPrivatePollsEvent.OnDeletePollIconClick -> {
                    _pollIdPendingDeletion.value = event.pollId
                }
                // When UserVotedOnPoll event is received, update the corresponding poll's UI model.
                is ViewPrivatePollsEvent.UserVotedOnPoll -> {
                    // Get the current state, ensuring it's Success.
                    val currentState = _uiState.value
                    if (currentState is ViewPrivatePollsState.Success) {
                        // Optimistically update the UI.
                        val updatedPolls = currentState.polls.map { poll ->
                            if (poll.pollId == event.pollId) {
                                poll.copy(userHasVoted = true)
                            } else {
                                poll
                            }
                        }
                        _uiState.value = ViewPrivatePollsState.Success(updatedPolls)

                        // Now, attempt to submit the vote to the repository.
                        val currentUserId: String? = userRepository.getCurrentUser()?.uid
                        if (currentUserId == null) {
                            Log.e(TAG, "UserVotedOnPoll: User not logged in. Cannot submit vote.")
                            _uiEffect.emit(ViewPrivatePollsEffect.ShowMessage("User not logged in. Please sign in to vote."))
                        } else {
                            Log.d(TAG, "UserVotedOnPoll: Submitting vote for poll ${event.pollId} by user $currentUserId")
                            // Construct the PollVote object
                            val pollVote = PollVote(
                                pollId = event.pollId,
                                voterId = currentUserId,
                                optionId = "dummy_option_id"
                                // voteId will be auto-generated or handled by repository/Firestore
                                // votedAt will default to Timestamp.now()
                            )
                            try {
                                pollsRepository.submitVote(pollVote)
                                Log.d(TAG, "UserVotedOnPoll: Vote submission successful for poll ${event.pollId}")
                                // Optionally, emit a success effect, though optimistic update might be enough.
                                // _uiEffect.emit(ViewPrivatePollsEffect.ShowMessage("Vote recorded!"))
                            } catch (e: Exception) {
                                Log.e(TAG, "UserVotedOnPoll: Failed to submit vote for poll ${event.pollId}", e)
                                _uiEffect.emit(ViewPrivatePollsEffect.ShowMessage("Failed to record vote: ${e.message}"))
                                // Here, we might consider reverting the optimistic UI update if the submission fails.
                                // For example, by reloading the polls or finding the specific poll and setting userHasVoted back to false.
                                // This depends on the desired UX. For now, we just show an error.
                            }
                        }
                    } else {
                        Log.w(TAG, "UserVotedOnPoll: Received event but current state is not Success. State: $currentState")
                    }
                }
                is ViewPrivatePollsEvent.ConfirmPollDeletion -> {
                    // This event is triggered when the user confirms the deletion in the dialog.
                    val pollIdToDelete = _pollIdPendingDeletion.value
                    if (pollIdToDelete != null) {
                        // Check if the current user is the creator before deleting.
                        // This is an additional safeguard, though the UI should ideally prevent non-creators from seeing the delete option.
                        val currentPollState = _uiState.value
                        if (currentPollState is ViewPrivatePollsState.Success) {
                            val poll = currentPollState.polls.find { it.pollId == pollIdToDelete }
                            val currentUserId = userRepository.getCurrentUser()?.uid
                            if (poll != null && poll.creatorId == currentUserId) {
                                // User is the creator, proceed with deletion.
                                deletePoll(pollIdToDelete)
                            } else {
                                // User is not the creator, or poll not found (should not happen if dialog was shown based on valid pollId).
                                _uiEffect.emit(ViewPrivatePollsEffect.ShowMessage("You can only delete polls you created."))
                                _pollIdPendingDeletion.value = null // Clear pending deletion
                            }
                        } else {
                            // Current state is not Success, cannot verify creator. Log and clear.
                            Log.w(TAG, "ConfirmPollDeletion: Cannot verify poll creator as current state is not Success.")
                            _uiEffect.emit(ViewPrivatePollsEffect.ShowMessage("Could not verify poll details. Please try again."))
                            _pollIdPendingDeletion.value = null // Clear pending deletion
                        }
                    } else {
                        // pollIdPendingDeletion was null, which is unexpected here.
                        Log.w(TAG, "ConfirmPollDeletion: pollIdPendingDeletion is null.")
                        _pollIdPendingDeletion.value = null // Ensure it's cleared
                    }
                }
                is ViewPrivatePollsEvent.DismissPollDeletionDialog -> {
                    // This event is triggered when the user cancels or dismisses the deletion dialog.
                    _pollIdPendingDeletion.value = null
                }
            }
        }
    }

    // This private helper function handles the actual poll deletion logic.
    private fun deletePoll(pollId: String) {
        viewModelScope.launch {
            // Set loading state for deletion or show a specific deletion progress indicator if needed.
            // For now, we directly call the repository.
            Log.d(TAG, "deletePoll: Attempting to delete poll with ID: $pollId")
            // Emit a loading state or message if desired, e.g.:
            // _uiEffect.emit(ViewPrivatePollsEffect.ShowMessage("Deleting poll..."))
            when (val result = pollsRepository.deletePoll(pollId)) {
                is app.donskey.cantdecide.util.Resource.Success -> {
                    Log.d(TAG, "deletePoll: Successfully deleted poll $pollId")
                    _uiEffect.emit(ViewPrivatePollsEffect.ShowMessage("Poll deleted successfully."))
                    // The poll list will update automatically due to Firestore real-time updates if it was part of the observed queries.
                    // If not, or for immediate UI feedback, one might need to manually refresh or remove the item from the local list.
                    // For now, relying on snapshot listeners to reflect the change.
                }
                is app.donskey.cantdecide.util.Resource.Error -> {
                    Log.e(TAG, "deletePoll: Error deleting poll $pollId: ${result.message}")
                    _uiEffect.emit(ViewPrivatePollsEffect.ShowMessage("Error deleting poll: ${result.message ?: "Unknown error"}"))
                }
                is app.donskey.cantdecide.util.Resource.Loading -> {
                    // Optionally handle loading state, e.g., show a specific UI or log.
                    Log.d(TAG, "deletePoll: Deletion in progress for poll $pollId...")
                    // You might want to show a global loading indicator or disable UI elements.
                }
            }
            _pollIdPendingDeletion.value = null // Clear pending deletion ID after operation
        }
    }

    // This function is responsible for fetching and processing the polls.
    private fun loadPolls() {
        // Launch a coroutine in the ViewModel's scope to handle asynchronous operations.
        viewModelScope.launch {
            // Set the UI state to Loading before starting to fetch data.
            _uiState.value = ViewPrivatePollsState.Loading
            Log.d(TAG, "loadPolls: Started. Fetching current user ID.")

            // Attempt to get the current user's ID.
            val currentUserId: String? = userRepository.getCurrentUser()?.uid

            // If the user ID is not available, it means the user is not logged in.
            // Set the state to Error and stop further processing.
            if (currentUserId == null) {
                Log.e(TAG, "loadPolls: Current user ID is null. Cannot load polls.")
                _uiState.value = ViewPrivatePollsState.Error("User not logged in. Please restart the app.")
                return@launch // Exit the coroutine.
            }
            Log.d(TAG, "loadPolls: Current User ID: $currentUserId")

            // Fetch two flows: one for polls created by the current user, and one for polls they are invited to.
            // These are Flows, meaning they can emit multiple values over time (e.g., if using Firestore snapshots).
            val pollsCreatedByUserFlow: Flow<List<PrivateTextPoll>> =
                pollsRepository.getPrivateTextPollsCreatedByCurrentUser(currentUserId)
            val pollsInvitedToFlow: Flow<List<PrivateTextPoll>> =
                pollsRepository.getPrivateTextPollsInvitedToCurrentUser(currentUserId)

            // Combine the results of both flows.
            // The combine operator takes the latest emissions from each flow and processes them.
            combine(pollsCreatedByUserFlow, pollsInvitedToFlow) { createdPolls, invitedPolls ->
                Log.d(TAG, "loadPolls: Received ${createdPolls.size} created polls and ${invitedPolls.size} invited polls.")
                // Concatenate the two lists of polls.
                val allPolls: List<PrivateTextPoll> = (createdPolls + invitedPolls)
                    // Remove duplicate polls if a poll appears in both lists (e.g., user invited to their own poll).
                    // distinctBy uses the pollId to determine uniqueness.
                    .distinctBy { it.pollId }
                    // Sort the combined list of polls by their creation date, with the newest first.
                    .sortedByDescending { it.createdAt }

                Log.d(TAG, "loadPolls: Total unique polls: ${allPolls.size}")
                // Transform each PrivateTextPoll data model into a PollListItemUiModel suitable for display.
                // transformToUiModel is now a suspend function.
                val uiModels = allPolls.map { textPoll ->
                    transformToUiModel(textPoll, currentUserId)
                }
                uiModels // Return the list of UI models
            }
            // Handle any exceptions that might occur during the flow collection (e.g., network errors).
            .catch { exception ->
                Log.e(TAG, "loadPolls: Error collecting combined polls flow: ${exception.message}", exception)
                // Set the UI state to Error, providing the exception message.
                _uiState.value = ViewPrivatePollsState.Error(exception.message ?: "Failed to load polls")
            }
            // Collect the final list of UI models emitted by the combined and transformed flow.
            .collect { pollUiModels ->
                Log.d(TAG, "loadPolls: Transformed ${pollUiModels.size} polls to UI models.")
                // If the list of UI models is empty, set the state to Empty.
                if (pollUiModels.isEmpty()) {
                    _uiState.value = ViewPrivatePollsState.Empty
                    Log.d(TAG, "loadPolls: No polls found, setting state to Empty.")
                } else {
                    // Otherwise, set the state to Success, providing the list of polls.
                    _uiState.value = ViewPrivatePollsState.Success(pollUiModels)
                    Log.d(TAG, "loadPolls: Successfully loaded polls, setting state to Success.")
                }
            }
        }
    }

    // This private helper function transforms a PrivateTextPoll data model into a PollListItemUiModel.
    private suspend fun transformToUiModel(poll: PrivateTextPoll, currentUserId: String): PollListItemUiModel {
        // Determine if the current user has voted on this poll by checking against the votedBy list.
        val currentUserHasVoted = poll.votedBy.contains(currentUserId)

        // Create a summary of the poll options, e.g., "3 Options".
        val optionsSummaryText = if (poll.options.size == 1) "1 Option" else "${poll.options.size} Options"

        // Fetch creator's avatar URL.
        var creatorAvatarUrl: String? = null
        try {
            // Assuming userRepository.getUserProfileById(creatorId) is a suspend function
            // that returns a UserProfile object (or similar) with a photoUrl property.
            val creatorProfile = userRepository.getUserProfileById(poll.creatorId)
            creatorAvatarUrl = creatorProfile?.photoUrl
        } catch (e: Exception) {
            Log.e(TAG, "Error fetching creator profile for ${poll.creatorId}: ${e.message}")
            // Keep creatorAvatarUrl as null if fetching fails.
        }

        // Log the details for debugging.
        Log.d(TAG, "Transforming poll ${poll.pollId}: User $currentUserId hasVoted=$currentUserHasVoted, VoteCount=${poll.voteCount}, Avatar: $creatorAvatarUrl")

        // Return a new PollListItemUiModel instance populated with data from the PrivateTextPoll.
        return PollListItemUiModel(
            pollId = poll.pollId,
            question = poll.question,
            creatorName = if (poll.creatorId == currentUserId) "You" else poll.creatorName, // Display "You" if creator is current user
            creatorId = poll.creatorId,
            creatorAvatarUrl = creatorAvatarUrl, // Restored avatar URL fetching
            optionsSummary = optionsSummaryText,
            numberOfOptions = poll.options.size,
            allowedVotersCount = poll.allowedVoters.size,
            anonymous = poll.anonymous,
            type = poll.type,
            createdAt = poll.createdAt,
            closesAt = poll.closesAt,
            userHasVoted = currentUserHasVoted, // Updated based on votedBy list.
            voteCount = poll.voteCount // Directly map from PrivateTextPoll.
        )
    }
} 