package app.donskey.cantdecide.ui.polls

import androidx.compose.foundation.layout.Column
import androidx.compose.foundation.layout.Spacer
import androidx.compose.foundation.layout.fillMaxSize
import androidx.compose.foundation.layout.fillMaxWidth
import androidx.compose.foundation.layout.height
import androidx.compose.foundation.layout.padding
import androidx.compose.foundation.rememberScrollState
import androidx.compose.foundation.shape.RoundedCornerShape
import androidx.compose.foundation.verticalScroll
import androidx.compose.material.icons.Icons
import androidx.compose.material.icons.automirrored.filled.List
import androidx.compose.material.icons.filled.AddCircle
import androidx.compose.material.icons.filled.History
import androidx.compose.material.icons.filled.Public
import androidx.compose.material3.Card
import androidx.compose.material3.CardDefaults
import androidx.compose.material3.ExperimentalMaterial3Api
import androidx.compose.material3.HorizontalDivider
import androidx.compose.material3.MaterialTheme
import androidx.compose.material3.Scaffold
import androidx.compose.material3.Surface
import androidx.compose.material3.Text
import androidx.compose.material3.TopAppBar
import androidx.compose.runtime.Composable
import androidx.compose.ui.Modifier
import androidx.compose.ui.res.stringResource
import androidx.compose.ui.text.font.FontWeight
import androidx.compose.ui.tooling.preview.Preview
import androidx.compose.ui.unit.dp
import androidx.navigation.NavController
import androidx.navigation.compose.rememberNavController
import app.donskey.cantdecide.R
import app.donskey.cantdecide.ui.theme.AppTheme
import app.donskey.cantdecide.ui.settings.SettingsItemRow

/**
 * Main screen for creating polls.
 * Displays options for creating different types of polls and viewing poll history.
 *
 * @param navController Navigation controller for navigating to different screens.
 */
@OptIn(ExperimentalMaterial3Api::class)
@Composable
fun CreatePollMainScreen(
    navController: NavController
) {
    // Scroll state for making the content scrollable
    val scrollState = rememberScrollState()
    
    Scaffold(
        topBar = {
            TopAppBar(
                title = { Text(text = stringResource(id = R.string.title_create_poll)) }
            )
        }
    ) { paddingValues ->
        Surface(
            modifier = Modifier
                .fillMaxSize()
                .padding(paddingValues),
            color = MaterialTheme.colorScheme.background
        ) {
            Column(
                modifier = Modifier
                    .fillMaxSize()
                    .padding(16.dp)
                    .verticalScroll(scrollState)
            ) {
                // Private Polls Card
                Card(
                    modifier = Modifier
                        .fillMaxWidth()
                        .padding(bottom = 16.dp),
                    shape = RoundedCornerShape(16.dp),
                    elevation = CardDefaults.cardElevation(defaultElevation = 4.dp),
                    colors = CardDefaults.cardColors(
                        containerColor = MaterialTheme.colorScheme.surface
                    )
                ) {
                    Column(
                        modifier = Modifier.padding(vertical = 8.dp)
                    ) {
                        Text(
                            text = "Private Polls",
                            style = MaterialTheme.typography.titleMedium,
                            fontWeight = FontWeight.Bold,
                            modifier = Modifier.padding(horizontal = 16.dp, vertical = 8.dp)
                        )
                        
                        // Create Private Polls
                        SettingsItemRow(
                            icon = Icons.Filled.AddCircle,
                            title = "Create Private Polls",
                            onClick = { navController.navigate("create_private_poll") }
                        )
                        
                        HorizontalDivider(
                            modifier = Modifier.padding(horizontal = 16.dp),
                            thickness = 0.5.dp,
                            color = MaterialTheme.colorScheme.outlineVariant
                        )
                        
                        // View Private Polls
                        SettingsItemRow(
                            icon = Icons.AutoMirrored.Filled.List,
                            title = "View Private Polls",
                            onClick = { navController.navigate("view_private_polls") }
                        )
                        
                        HorizontalDivider(
                            modifier = Modifier.padding(horizontal = 16.dp),
                            thickness = 0.5.dp,
                            color = MaterialTheme.colorScheme.outlineVariant
                        )
                        
                        // Private Polls History
                        SettingsItemRow(
                            icon = Icons.Filled.History,
                            title = "Private Polls History",
                            onClick = { navController.navigate("private_polls_history") }
                        )
                    }
                }
                
                // Public Polls Card
                Card(
                    modifier = Modifier
                        .fillMaxWidth()
                        .padding(bottom = 16.dp),
                    shape = RoundedCornerShape(16.dp),
                    elevation = CardDefaults.cardElevation(defaultElevation = 4.dp),
                    colors = CardDefaults.cardColors(
                        containerColor = MaterialTheme.colorScheme.surface
                    )
                ) {
                    Column(
                        modifier = Modifier.padding(vertical = 8.dp)
                    ) {
                        Text(
                            text = "Public Polls",
                            style = MaterialTheme.typography.titleMedium,
                            fontWeight = FontWeight.Bold,
                            modifier = Modifier.padding(horizontal = 16.dp, vertical = 8.dp)
                        )
                        
                        // Create Public Polls
                        SettingsItemRow(
                            icon = Icons.Filled.Public,
                            title = "Create Public Polls",
                            onClick = { navController.navigate("create_public_poll") }
                        )
                        
                        HorizontalDivider(
                            modifier = Modifier.padding(horizontal = 16.dp),
                            thickness = 0.5.dp,
                            color = MaterialTheme.colorScheme.outlineVariant
                        )
                        
                        // View Public Polls
                        SettingsItemRow(
                            icon = Icons.AutoMirrored.Filled.List,
                            title = "View Public Polls",
                            onClick = { navController.navigate("view_public_polls") }
                        )
                        
                        HorizontalDivider(
                            modifier = Modifier.padding(horizontal = 16.dp),
                            thickness = 0.5.dp,
                            color = MaterialTheme.colorScheme.outlineVariant
                        )
                        
                        // Public Polls History
                        SettingsItemRow(
                            icon = Icons.Filled.History,
                            title = "Public Polls History",
                            onClick = { navController.navigate("public_polls_history") }
                        )
                    }
                }
                
                // Add space at the bottom for better scrolling experience
                Spacer(modifier = Modifier.height(80.dp))
            }
        }
    }
}

@Preview(showBackground = true)
@Composable
fun CreatePollMainScreenPreview() {
    AppTheme {
        CreatePollMainScreen(
            navController = rememberNavController()
        )
    }
} 