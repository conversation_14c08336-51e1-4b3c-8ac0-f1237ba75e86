package app.donskey.cantdecide.presentation.base

import androidx.lifecycle.ViewModel
import androidx.lifecycle.viewModelScope
import kotlinx.coroutines.channels.Channel
import kotlinx.coroutines.flow.MutableSharedFlow
import kotlinx.coroutines.flow.MutableStateFlow
import kotlinx.coroutines.flow.asSharedFlow
import kotlinx.coroutines.flow.asStateFlow
import kotlinx.coroutines.flow.receiveAsFlow
import kotlinx.coroutines.launch

/**
 * Abstract base class for ViewModels implementing the MVI (Model-View-Intent) pattern.
 *
 * @param E The type of ViewEvent that this ViewModel can handle.
 * @param S The type of ViewState that this ViewModel manages.
 * @param F The type of ViewEffect that this ViewModel can produce.
 */
abstract class BaseViewModel<E : ViewEvent, S : ViewState, F : ViewEffect> : ViewModel() {

    // Creates the initial state for the ViewModel.
    // This must be implemented by subclasses.
    abstract fun createInitialState(): S

    // Holds the current state of the UI.
    // It's a MutableStateFlow so that Composable<PERSON> can observe it for changes.
    private val _uiState: MutableStateFlow<S> by lazy { MutableStateFlow(createInitialState()) }
    val uiState = _uiState.asStateFlow()

    // Used to send one-time events (intents) from the UI to the ViewModel.
    // It's a MutableSharedFlow to handle multiple collectors if needed, though typically one.
    private val _event: MutableSharedFlow<E> = MutableSharedFlow()
    val event = _event.asSharedFlow()

    // Used to send one-time side effects from the ViewModel to the UI (e.g., navigation, Toasts).
    // It's a Channel because effects are usually consumed once.
    private val _effect: Channel<F> = Channel()
    val effect = _effect.receiveAsFlow()

    init {
        // Start collecting events as soon as the ViewModel is initialized.
        subscribeToEvents()
    }

    /**
     * Subscribes to the event flow and calls handleEvent for each incoming event.
     */
    private fun subscribeToEvents() {
        viewModelScope.launch {
            event.collect {
                handleEvent(it)
            }
        }
    }

    /**
     * Abstract function to be implemented by subclasses to handle specific events.
     * @param event The event to handle.
     */
    abstract fun handleEvent(event: E)

    /**
     * Allows the UI to send an event to this ViewModel.
     * @param event The event to send.
     */
    fun setEvent(event: E) {
        viewModelScope.launch { _event.emit(event) }
    }

    /**
     * Allows subclasses to update the UI state.
     * The provided [reduce] function takes the current state and returns the new state.
     * @param reduce A lambda function that transforms the current state to a new state.
     */
    protected fun setState(reduce: S.() -> S) {
        val newState = uiState.value.reduce()
        _uiState.value = newState
    }

    /**
     * Allows subclasses to send a one-time side effect to the UI.
     * @param effectBuilder A lambda function that produces the effect to send.
     */
    protected fun setEffect(effectBuilder: () -> F) {
        val effectValue = effectBuilder()
        viewModelScope.launch { _effect.send(effectValue) }
    }
} 