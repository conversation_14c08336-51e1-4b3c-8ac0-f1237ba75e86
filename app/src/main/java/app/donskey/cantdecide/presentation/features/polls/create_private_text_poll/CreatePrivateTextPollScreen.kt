package app.donskey.cantdecide.presentation.features.polls.create_private_text_poll

import android.util.Log
import android.widget.Toast
import androidx.compose.foundation.ExperimentalFoundationApi
import androidx.compose.foundation.layout.*
import androidx.compose.foundation.relocation.BringIntoViewRequester
import androidx.compose.foundation.relocation.bringIntoViewRequester
import androidx.compose.foundation.rememberScrollState
import androidx.compose.foundation.text.KeyboardActions
import androidx.compose.foundation.text.KeyboardOptions
import androidx.compose.foundation.verticalScroll
import androidx.compose.material.icons.Icons
import androidx.compose.material.icons.automirrored.filled.ArrowBack
import androidx.compose.material.icons.filled.Clear
import androidx.compose.material.icons.filled.EditCalendar
import androidx.compose.material3.*
import androidx.compose.runtime.*
import androidx.compose.ui.Alignment
import androidx.compose.ui.Modifier
import androidx.compose.ui.focus.FocusDirection
import androidx.compose.ui.focus.FocusRequester
import androidx.compose.ui.focus.focusRequester
import androidx.compose.ui.focus.onFocusEvent
import androidx.compose.ui.platform.LocalContext
import androidx.compose.ui.platform.LocalFocusManager
import androidx.compose.ui.text.input.ImeAction
import androidx.compose.ui.text.input.KeyboardCapitalization
import androidx.compose.ui.unit.dp
import androidx.hilt.navigation.compose.hiltViewModel
import java.text.SimpleDateFormat
import java.util.Calendar
import java.util.Locale
import kotlinx.coroutines.delay
import kotlinx.coroutines.flow.collectLatest
import kotlinx.coroutines.launch
import androidx.navigation.NavController
import androidx.compose.material3.DatePickerDialog
import androidx.compose.material3.HorizontalDivider
import androidx.compose.material3.TextButton
import androidx.compose.foundation.clickable
import androidx.compose.material3.RadioButton
import androidx.compose.material3.Checkbox
import androidx.compose.ui.text.font.FontWeight
import coil.compose.AsyncImage
import coil.request.ImageRequest
import androidx.compose.foundation.Image
import androidx.compose.foundation.layout.size
import androidx.compose.foundation.shape.CircleShape
import androidx.compose.material.icons.filled.Person
import androidx.compose.ui.draw.clip
import androidx.compose.foundation.background
import androidx.compose.ui.res.painterResource
import app.donskey.cantdecide.R

/**
 * Composable screen for creating a new private text poll.
 * Allows users to input a poll question, define options, and configure other poll settings.
 */
@OptIn(ExperimentalMaterial3Api::class, ExperimentalFoundationApi::class)
@Composable
fun CreatePrivateTextPollScreen(
    viewModel: CreatePrivateTextPollViewModel = hiltViewModel(),
    navController: NavController
) {
    // Observe the state from the ViewModel.
    val state by viewModel.uiState.collectAsState()
    Log.d("CreatePollScreen", "Recomposition: state.numberOfOptions = ${state.numberOfOptions}")
    // Get the current context for showing Toasts.
    val context = LocalContext.current
    // Get the focus manager for controlling keyboard focus.
    val focusManager = LocalFocusManager.current
    // Coroutine scope for launching async tasks.
    val coroutineScope = rememberCoroutineScope()

    // Focus requesters for input fields
    val pollQuestionFocusRequester = remember { FocusRequester() }
    val pollQuestionBringIntoViewRequester = remember { BringIntoViewRequester() } // For poll question
    val optionFocusRequesters = remember(state.numberOfOptions) {
        Log.d("FocusDebug", "[REMEMBER] Creating optionFocusRequesters for ${state.numberOfOptions} options.")
        List(state.numberOfOptions) { FocusRequester() }
    }
    val friendsSectionBringIntoViewRequester = remember { BringIntoViewRequester() } // For friends error scrolling

    // Removed the LaunchedEffect that handled FocusFirstError.
    // General effects (Toast, Navigate) are handled here.
    LaunchedEffect(key1 = Unit) { // Re-key to Unit as it only handles non-UI-state-dependent effects now, or could be viewModel.effect
        viewModel.effect.collectLatest {
            effect ->
            Log.d("FocusDebug", "[EFFECT General] Received: $effect")
            when (effect) {
                is CreatePrivateTextPollContract.Effect.ShowError -> {
                    Toast.makeText(context, effect.message, Toast.LENGTH_SHORT).show()
                }
                is CreatePrivateTextPollContract.Effect.NavigateBack -> {
                    navController.popBackStack()
                }
                // FocusFirstError no longer exists as an Effect.
            }
        }
    }

    // SideEffect to handle focusing on the first error after validation.
    // This runs after every successful composition where its read-state changes.
    SideEffect {
        if (state.needsToFocusAfterValidation) {
            Log.d("FocusDebug", "[SideEffect FocusValidation] needsToFocusAfterValidation is true. QuestionError: ${state.pollQuestionError}")
            coroutineScope.launch {
                delay(100) 
                if (state.pollQuestionError != null) {
                    Log.d("FocusDebug", "[SideEffect FocusValidation] Attempting to focus Poll Question.")
                    pollQuestionFocusRequester.requestFocus()
                } else {
                    val firstErrorOptionIndex = state.pollOptionErrors.indexOfFirst { it != null }
                    if (firstErrorOptionIndex != -1) {
                        if (firstErrorOptionIndex < optionFocusRequesters.size) {
                            Log.d("FocusDebug", "[SideEffect FocusValidation] Attempting to focus Option $firstErrorOptionIndex. Requesters size: ${optionFocusRequesters.size}")
                            try {
                                optionFocusRequesters[firstErrorOptionIndex].requestFocus()
                            } catch (e: IllegalStateException) {
                                Log.e("FocusDebug", "[SideEffect FocusValidation] ILLEGAL STATE for option $firstErrorOptionIndex: ${e.message}")
                            }
                        } else {
                            Log.d("FocusDebug", "[SideEffect FocusValidation] Index $firstErrorOptionIndex OOB for requesters (size ${optionFocusRequesters.size}). NumOptions: ${state.numberOfOptions}")
                        }
                    }
                }
                viewModel.setEvent(CreatePrivateTextPollContract.Event.ClearFocusAfterValidationFlag)
            }
        }
        // Handle scrolling to friend error section
        if (state.needsToFocusFriendError) {
            Log.d("FocusDebug", "[SideEffect FocusFriendError] needsToFocusFriendError is true.")
            coroutineScope.launch {
                delay(100) // Ensure UI has settled
                try {
                    friendsSectionBringIntoViewRequester.bringIntoView()
                } catch (e: Exception) {
                    Log.e("FocusDebug", "[SideEffect FocusFriendError] Error bringing friends section into view: ${e.message}")
                }
                viewModel.setEvent(CreatePrivateTextPollContract.Event.ClearFocusFriendErrorFlag)
            }
        }
    }

    // Material 3 DatePicker state
    val datePickerState = rememberDatePickerState(
        initialSelectedDateMillis = state.expiryDate, // Initialize with current expiry date
        selectableDates = object : SelectableDates {
            override fun isSelectableDate(utcTimeMillis: Long): Boolean {
                val today = Calendar.getInstance().apply {
                    timeInMillis = System.currentTimeMillis() // Ensure it starts from current time
                    set(Calendar.HOUR_OF_DAY, 0)
                    set(Calendar.MINUTE, 0)
                    set(Calendar.SECOND, 0)
                    set(Calendar.MILLISECOND, 0)
                }
                return utcTimeMillis >= today.timeInMillis
            }

            override fun isSelectableYear(year: Int): Boolean {
                return year >= Calendar.getInstance().get(Calendar.YEAR)
            }
        }
    )

    // Main container for the screen content.
    Scaffold(
        topBar = {
            TopAppBar(
                title = { Text("Create Private Text Poll") },
                navigationIcon = {
                    IconButton(onClick = { navController.popBackStack() }) {
                        Icon(Icons.AutoMirrored.Filled.ArrowBack, contentDescription = "Back")
                    }
                },
                colors = TopAppBarDefaults.topAppBarColors(
                    containerColor = MaterialTheme.colorScheme.primary,
                    titleContentColor = MaterialTheme.colorScheme.onPrimary,
                    navigationIconContentColor = MaterialTheme.colorScheme.onPrimary
                )
            )
        },
        bottomBar = {
            Surface(
                modifier = Modifier.fillMaxWidth(),
                shadowElevation = 8.dp // Optional: adds a shadow for visual separation
            ) {
                Button(
                    onClick = { 
                        Log.d("FocusDebug", "Create Poll button clicked.")
                        focusManager.clearFocus(true) // Force clear focus before processing event
                        // Launch a coroutine to add a small delay, allowing focus clear to propagate
                        // before ViewModel processes the click. This helps ensure latest text state is validated.
                        coroutineScope.launch {
                            delay(50) // Brief delay, can be tuned (e.g., 20ms or 50ms)
                            viewModel.setEvent(CreatePrivateTextPollContract.Event.OnCreatePollClicked) 
                        }
                    },
                    modifier = Modifier
                        .fillMaxWidth()
                        .padding(horizontal = 16.dp, vertical = 12.dp), // Standard padding for a button in a bottom bar
                    enabled = !state.isLoading // CORRECTED: Button enabled unless loading
                ) {
                    if (state.isLoading) {
                        CircularProgressIndicator(modifier = Modifier.size(24.dp))
                    } else {
                        Text("Create Poll")
                    }
                }
            }
        }
    ) { innerPadding -> // These paddingValues from Scaffold account for topBar, bottomBar, and system insets (including IME)
        Column(
            modifier = Modifier
                .fillMaxSize()
                .padding(innerPadding) // Apply padding from Scaffold
                .imePadding() // Ensure the scrollable area is pushed up by the keyboard
                .padding(horizontal = 16.dp) // Screen-specific horizontal padding for content
                .verticalScroll(rememberScrollState())
            // No .weight(1f) as this is the main scrollable content area now
        ) {
            // --- Number of Options Section (MOVED UP) ---
            var expanded by remember { mutableStateOf(false) }
            val optionsRange = (CreatePrivateTextPollDefaults.MIN_POLL_OPTIONS..CreatePrivateTextPollDefaults.MAX_POLL_OPTIONS).toList()

            Row(
                verticalAlignment = Alignment.CenterVertically,
                modifier = Modifier.fillMaxWidth().padding(top = 16.dp) // Added top padding
            ) {
                Text("Number of Options: ", modifier = Modifier.padding(end = 8.dp))
                Text("(Max ${CreatePrivateTextPollDefaults.MAX_POLL_OPTIONS})", style = MaterialTheme.typography.bodySmall)
            }
            ExposedDropdownMenuBox(
                expanded = expanded,
                onExpandedChange = { expanded = !expanded },
                modifier = Modifier.fillMaxWidth()
            ) {
                Log.d("CreatePollScreen", "Dropdown: state.numberOfOptions = ${state.numberOfOptions}")
                OutlinedTextField(
                    value = state.numberOfOptions.toString(),
                    onValueChange = {},
                    readOnly = true,
                    label = { Text("Select Options") },
                    trailingIcon = { ExposedDropdownMenuDefaults.TrailingIcon(expanded = expanded) },
                    colors = ExposedDropdownMenuDefaults.outlinedTextFieldColors(),
                    modifier = Modifier
                        .menuAnchor()
                        .fillMaxWidth()
                )
                ExposedDropdownMenu(
                    expanded = expanded,
                    onDismissRequest = { expanded = false },
                ) {
                    optionsRange.forEach { selectionOption ->
                        DropdownMenuItem(
                            text = { Text(selectionOption.toString()) },
                            onClick = {
                                viewModel.setEvent(CreatePrivateTextPollContract.Event.OnNumberOfOptionsChanged(selectionOption))
                                expanded = false
                            },
                            modifier = Modifier.fillMaxWidth()
                        )
                    }
                }
            }
            Spacer(modifier = Modifier.height(16.dp)) // Spacer after dropdown
            // --- End of Number of Options Section ---

            // --- Poll Question Section (MOVED DOWN) ---
            Column(
                modifier = Modifier
                    .fillMaxWidth()
                    .bringIntoViewRequester(pollQuestionBringIntoViewRequester)
            ) {
                OutlinedTextField(
                    value = state.pollQuestion,
                    onValueChange = { 
                        // Enforce character limit for poll question
                        if (it.length <= CreatePrivateTextPollDefaults.MAX_QUESTION_LENGTH) {
                            viewModel.setEvent(CreatePrivateTextPollContract.Event.OnPollQuestionChanged(it))
                        }
                    },
                    label = { Text("Poll Question") },
                    modifier = Modifier
                        .fillMaxWidth()
                        // .padding(top = 16.dp) // Top padding removed as it's now after dropdown
                        .focusRequester(pollQuestionFocusRequester)
                        .onFocusEvent { focusState -> 
                            Log.d("FocusDebug", "Poll Question onFocusEvent: isFocused = ${focusState.isFocused}")
                            if (focusState.isFocused) {
                                coroutineScope.launch {
                                    delay(200) 
                                    Log.d("FocusDebug", "Poll Question isFocused = true, calling bringIntoView().")
                                    pollQuestionBringIntoViewRequester.bringIntoView()
                                }
                            }
                        },
                    keyboardOptions = KeyboardOptions.Default.copy(
                        capitalization = KeyboardCapitalization.Sentences,
                        imeAction = ImeAction.Next
                    ),
                    keyboardActions = KeyboardActions(
                        onNext = {
                            Log.d("FocusDebug", "Poll Question 'Next' action: Attempting to move focus to first option.")
                            if (state.pollOptions.isNotEmpty() && optionFocusRequesters.isNotEmpty()) {
                                try {
                                    optionFocusRequesters[0].requestFocus()
                                } catch (e: IllegalStateException) {
                                    Log.e("FocusDebug", "Poll Question 'Next' action: Error focusing option 0: ${e.message}")
                                    focusManager.moveFocus(FocusDirection.Down) 
                                }
                            } else {
                                Log.d("FocusDebug", "Poll Question 'Next' action: No options to focus, using fallback.")
                                focusManager.moveFocus(FocusDirection.Down) 
                            }
                        }
                    ),
                    singleLine = false,
                    maxLines = 3,
                    isError = state.pollQuestionError != null,
                    // Supporting text to show character count
                    supportingText = {
                        Text(
                            text = "${state.pollQuestion.length} / ${CreatePrivateTextPollDefaults.MAX_QUESTION_LENGTH}",
                            modifier = Modifier.fillMaxWidth(),
                            textAlign = androidx.compose.ui.text.style.TextAlign.End,
                        )
                    }
                )
                if (state.pollQuestionError != null) {
                    Text(
                        text = state.pollQuestionError ?: "",
                        color = MaterialTheme.colorScheme.error,
                        style = MaterialTheme.typography.bodySmall,
                        modifier = Modifier.padding(start = 16.dp)
                    )
                }
            }
            // --- End of Poll Question Section ---

            Spacer(modifier = Modifier.height(16.dp)) // Spacer after Poll Question

            // --- Dynamically Generated Option TextFields ---
            state.pollOptions.forEachIndexed { index, optionText ->
                val bringIntoViewRequester = remember { BringIntoViewRequester() }
                // optionFocusRequester is now accessed from the list `optionFocusRequesters`

                Column(
                    modifier = Modifier
                        .fillMaxWidth()
                        .bringIntoViewRequester(bringIntoViewRequester)
                ) {
                    OutlinedTextField(
                        value = optionText,
                        onValueChange = { text ->
                            // Enforce character limit for poll options
                            if (text.length <= CreatePrivateTextPollDefaults.MAX_OPTION_LENGTH) {
                                viewModel.setEvent(CreatePrivateTextPollContract.Event.OnPollOptionChanged(index, text))
                            }
                        },
                        label = { Text("Option ${index + 1}") },
                        modifier = Modifier
                            .fillMaxWidth()
                            .padding(vertical = 4.dp)
                            .focusRequester(optionFocusRequesters[index]) // Apply indexed focus requester
                            .onFocusEvent { focusState ->
                                Log.d("FocusDebug", "Option $index onFocusEvent: isFocused = ${focusState.isFocused}")
                                if (focusState.isFocused) {
                                    coroutineScope.launch {
                                        delay(200) // Delay to allow keyboard to animate
                                        Log.d("FocusDebug", "Option $index isFocused = true, calling bringIntoView().")
                                        bringIntoViewRequester.bringIntoView()
                                    }
                                }
                            },
                        keyboardOptions = KeyboardOptions.Default.copy(
                            capitalization = KeyboardCapitalization.Sentences,
                            imeAction = if (index == state.numberOfOptions - 1) ImeAction.Done else ImeAction.Next
                        ),
                        keyboardActions = KeyboardActions(
                            onNext = { focusManager.moveFocus(FocusDirection.Down) },
                            onDone = { focusManager.clearFocus() }
                        ),
                        singleLine = true,
                        isError = state.pollOptionErrors.getOrNull(index) != null,
                        // Supporting text to show character count
                        supportingText = {
                            Text(
                                text = "${optionText.length} / ${CreatePrivateTextPollDefaults.MAX_OPTION_LENGTH}",
                                modifier = Modifier.fillMaxWidth(),
                                textAlign = androidx.compose.ui.text.style.TextAlign.End,
                            )
                        }
                    )
                    if (state.pollOptionErrors.getOrNull(index) != null) {
                        Text(
                            text = state.pollOptionErrors[index] ?: "",
                            color = MaterialTheme.colorScheme.error,
                            style = MaterialTheme.typography.bodySmall,
                            modifier = Modifier.padding(start = 16.dp)
                        )
                    }
                    Spacer(modifier = Modifier.height(4.dp))
                }
            }
            // --- End of Dynamically Generated Option TextFields ---

            // --- Expiry Date Section (Order unchanged relative to options) ---
            Spacer(modifier = Modifier.height(24.dp)) // Space before expiry date section
            HorizontalDivider(modifier = Modifier.padding(vertical = 8.dp)) // Added Divider

            Text(
                text = "Optional: Set Expiry Date",
                style = MaterialTheme.typography.titleMedium,
                modifier = Modifier.padding(bottom = 8.dp)
            )

            Row(
                modifier = Modifier
                    .fillMaxWidth()
                    .padding(vertical = 8.dp),
                verticalAlignment = Alignment.CenterVertically,
                horizontalArrangement = Arrangement.SpaceBetween
            ) {
                val dateFormatter = remember { SimpleDateFormat("dd MMM yyyy", Locale.getDefault()) }
                Text(
                    text = state.expiryDate?.let { dateFormatter.format(it) } ?: "Not set",
                    style = MaterialTheme.typography.bodyLarge,
                    modifier = Modifier.weight(1f)
                )
                Row {
                    IconButton(
                        onClick = { viewModel.setEvent(CreatePrivateTextPollContract.Event.OnToggleDatePickerDialog) },
                        // modifier = Modifier.size(40.dp) // Optional: Increase touch target of IconButton itself
                    ) {
                        Icon(
                            imageVector = Icons.Filled.EditCalendar,
                            contentDescription = "Select Expiry Date",
                            modifier = Modifier.size(36.dp)
                        )
                    }
                    if (state.expiryDate != null) {
                        IconButton(onClick = { 
                            viewModel.setEvent(CreatePrivateTextPollContract.Event.OnClearExpiryIconClicked)
                        }) {
                            Icon(
                                imageVector = Icons.Filled.Clear,
                                contentDescription = "Clear Expiry Date",
                                modifier = Modifier.size(36.dp)
                            )
                        }
                    }
                }
            }
            // --- End of Expiry Date Section ---

            if (state.showDatePickerDialog) {
                DatePickerDialog(
                    onDismissRequest = { viewModel.setEvent(CreatePrivateTextPollContract.Event.OnToggleDatePickerDialog) },
                    confirmButton = {
                        TextButton(
                            onClick = {
                                val selectedDateMillis = datePickerState.selectedDateMillis
                                val calendar = Calendar.getInstance()
                                selectedDateMillis?.let {
                                   calendar.timeInMillis = it
                                   calendar.set(Calendar.HOUR_OF_DAY, 23)
                                   calendar.set(Calendar.MINUTE, 59)
                                   calendar.set(Calendar.SECOND, 59)
                                   calendar.set(Calendar.MILLISECOND, 999)
                                }
                                viewModel.setEvent(CreatePrivateTextPollContract.Event.OnExpiryDateChanged(calendar.timeInMillis.takeIf { selectedDateMillis != null }))
                            }
                        ) { Text("OK") }
                    },
                    dismissButton = {
                        TextButton(onClick = { viewModel.setEvent(CreatePrivateTextPollContract.Event.OnToggleDatePickerDialog) }) {
                            Text("Cancel")
                        }
                    }
                ) {
                    DatePicker(state = datePickerState)
                }
            }

            // --- Confirmation Dialog for Clearing Expiry Date ---
            if (state.showClearExpiryConfirmDialog) {
                AlertDialog(
                    onDismissRequest = { viewModel.setEvent(CreatePrivateTextPollContract.Event.OnToggleClearExpiryConfirmDialog) },
                    title = { Text("Confirm Clear Date") },
                    text = { Text("Are you sure you want to remove the expiry date?") },
                    confirmButton = {
                        TextButton(
                            onClick = { 
                                viewModel.setEvent(CreatePrivateTextPollContract.Event.OnConfirmClearExpiryDate)
                            }
                        ) { Text("Yes, Clear") }
                    },
                    dismissButton = {
                        TextButton(
                            onClick = { 
                                viewModel.setEvent(CreatePrivateTextPollContract.Event.OnToggleClearExpiryConfirmDialog) 
                            }
                        ) { Text("No, Keep") }
                    }
                )
            }

            // --- Poll Type RadioButtons (Order unchanged relative to options/expiry) ---
            HorizontalDivider(modifier = Modifier.padding(vertical = 8.dp))
            Text(
                text = "Poll Type",
                style = MaterialTheme.typography.titleMedium,
                modifier = Modifier.padding(bottom = 4.dp)
            )
            Row(Modifier.fillMaxWidth(), horizontalArrangement = Arrangement.Start) {
                Row(
                    verticalAlignment = Alignment.CenterVertically,
                    modifier = Modifier
                        .clickable { viewModel.setEvent(CreatePrivateTextPollContract.Event.OnPollAnonymityChanged(false)) }
                        .padding(end = 16.dp) // Spacing between radio buttons
                ) {
                    RadioButton(
                        selected = !state.anonymous,
                        onClick = { viewModel.setEvent(CreatePrivateTextPollContract.Event.OnPollAnonymityChanged(false)) }
                    )
                    Text("Non-Anonymous", style = MaterialTheme.typography.bodyLarge, modifier = Modifier.padding(start = 4.dp))
                }
                Row(
                    verticalAlignment = Alignment.CenterVertically,
                    modifier = Modifier.clickable { viewModel.setEvent(CreatePrivateTextPollContract.Event.OnPollAnonymityChanged(true)) }
                ) {
                    RadioButton(
                        selected = state.anonymous,
                        onClick = { viewModel.setEvent(CreatePrivateTextPollContract.Event.OnPollAnonymityChanged(true)) }
                    )
                    Text("Anonymous", style = MaterialTheme.typography.bodyLarge, modifier = Modifier.padding(start = 4.dp))
                }
            }
            HorizontalDivider(modifier = Modifier.padding(top = 8.dp))
            // --- End of Poll Type RadioButtons ---

            // --- Friends Selection Section (Order unchanged relative to options/expiry/poll type) ---
            if (!state.anonymous) {
                Column {
                    Spacer(modifier = Modifier.height(16.dp))
                    Text(
                        text = "Invite Friends to Vote",
                        style = MaterialTheme.typography.titleMedium,
                        modifier = Modifier.padding(bottom = 8.dp)
                    )
                    val allFriendsSelected = state.friendsList.isNotEmpty() && state.friendsList.all { it.isSelected }
                    Row(
                        verticalAlignment = Alignment.CenterVertically,
                        modifier = Modifier
                            .fillMaxWidth()
                            .clickable { 
                                focusManager.clearFocus() 
                                viewModel.setEvent(CreatePrivateTextPollContract.Event.OnSelectAllFriendsClicked(!allFriendsSelected))
                            }
                            .padding(vertical = 4.dp)
                    ) {
                        Checkbox(
                            checked = allFriendsSelected,
                            onCheckedChange = { 
                                focusManager.clearFocus() 
                                viewModel.setEvent(CreatePrivateTextPollContract.Event.OnSelectAllFriendsClicked(it)) 
                            },
                        )
                        Text(
                            text = if (allFriendsSelected) "Deselect All Friends" else "Select All Friends",
                            style = MaterialTheme.typography.bodyLarge,
                            fontWeight = FontWeight.Bold, // Added Bold
                            modifier = Modifier.padding(start = 8.dp)
                        )
                    }
                    HorizontalDivider()

                    // Friends List
                    if (state.friendsList.isEmpty()) {
                        Text(
                            text = "No friends available to invite. Add friends first!",
                            style = MaterialTheme.typography.bodyMedium,
                            modifier = Modifier.padding(vertical = 16.dp)
                        )
                    } else {
                        Column(
                            modifier = Modifier.fillMaxWidth()
                        ) {
                            state.friendsList.forEach { friend -> 
                                Row(
                                    verticalAlignment = Alignment.CenterVertically,
                                    modifier = Modifier
                                        .fillMaxWidth()
                                        .clickable { 
                                            focusManager.clearFocus() 
                                            viewModel.setEvent(CreatePrivateTextPollContract.Event.OnFriendSelected(friend.id, !friend.isSelected))
                                        }
                                        .padding(vertical = 8.dp)
                                ) {
                                    Checkbox(
                                        checked = friend.isSelected,
                                        onCheckedChange = { isChecked -> 
                                            focusManager.clearFocus() 
                                            viewModel.setEvent(CreatePrivateTextPollContract.Event.OnFriendSelected(friend.id, isChecked)) 
                                        }
                                    )
                                    Spacer(modifier = Modifier.width(8.dp)) // Add spacer between checkbox and avatar

                                    // Avatar Image
                                    Box(modifier = Modifier.size(40.dp).clip(CircleShape)) {
                                        if (!friend.photoUrl.isNullOrEmpty()) {
                                            AsyncImage(
                                                model = ImageRequest.Builder(LocalContext.current)
                                                    .data(friend.photoUrl)
                                                    .crossfade(true)
                                                    .build(),
                                                contentDescription = "${friend.name} avatar",
                                                modifier = Modifier.fillMaxSize(),
                                                contentScale = androidx.compose.ui.layout.ContentScale.Crop,
                                                // Placeholder and error can be more sophisticated if needed
                                                placeholder = painterResource(id = R.drawable.default_profile_pic), // Using your actual placeholder
                                                error = painterResource(id = R.drawable.default_profile_pic) // Using your actual placeholder
                                            )
                                        } else {
                                            // Placeholder Icon when photoUrl is null or empty
                                            Image(
                                                imageVector = Icons.Filled.Person,
                                                contentDescription = "Default avatar placeholder",
                                                modifier = Modifier
                                                    .fillMaxSize()
                                                    .background(MaterialTheme.colorScheme.surfaceVariant), // Placeholder background
                                                contentScale = androidx.compose.ui.layout.ContentScale.Fit,
                                                colorFilter = androidx.compose.ui.graphics.ColorFilter.tint(MaterialTheme.colorScheme.onSurfaceVariant)
                                            )
                                        }
                                    }
                                    Text(
                                        text = friend.name,
                                        style = MaterialTheme.typography.bodyLarge,
                                        modifier = Modifier.padding(start = 8.dp)
                                    )
                                }
                                HorizontalDivider()
                            }
                        }
                    }
                } // End of the Column that groups the main friends content
                
                // Display friend selection error
                val currentFriendSelectionError = state.friendSelectionError 
                if (currentFriendSelectionError != null) {
                    Text(
                        text = currentFriendSelectionError, 
                        color = MaterialTheme.colorScheme.error,
                        style = MaterialTheme.typography.bodySmall,
                        modifier = Modifier
                            .padding(start = 16.dp, top = 4.dp)
                            .bringIntoViewRequester(friendsSectionBringIntoViewRequester) 
                    )
                }
                HorizontalDivider(modifier = Modifier.padding(top = 8.dp)) 
            }
            // --- End of Friends Selection Section ---

            Spacer(modifier = Modifier.height(160.dp)) // INCREASED FINAL SPACER (e.g., from 80.dp)
        }
    }
}

// TODO: Add a @Preview Composable for this screen to facilitate UI development. 