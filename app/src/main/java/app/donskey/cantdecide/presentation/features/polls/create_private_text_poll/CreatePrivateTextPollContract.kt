package app.donskey.cantdecide.presentation.features.polls.create_private_text_poll

import app.donskey.cantdecide.presentation.base.ViewEffect
import app.donskey.cantdecide.presentation.base.ViewEvent
import app.donskey.cantdecide.presentation.base.ViewState

object CreatePrivateTextPollDefaults {
    //const val DEFAULT_POLL_QUESTION: String = ""
    const val MIN_POLL_OPTIONS: Int = 2
    const val MAX_POLL_OPTIONS: Int = 10
    const val DEFAULT_NUMBER_OF_OPTIONS: Int = 2
    const val MAX_QUESTION_LENGTH = 100
    const val MAX_OPTION_LENGTH = 50
}

/**
 * Defines the contract for the Create Private Text Poll screen, including its state, events, and effects.
 */
class CreatePrivateTextPollContract {

    /**
     * Represents an item in the selectable friends list.
     * @param id Unique identifier for the friend.
     * @param name Display name of the friend.
     * @param isSelected True if the friend is currently selected, false otherwise.
     * @param photoUrl Optional: URL to the friend's photo
     */
    data class FriendListItem(
        val id: String,
        val name: String,
        val isSelected: Boolean = false,
        val photoUrl: String? = null
    )

    /**
     * Represents the state of the Create Private Text Poll screen.
     *
     * @property pollQuestion The current text of the poll question.
     * @property numberOfOptions The selected number of options for the poll.
     * @property pollOptions A list containing the text for each poll option.
     * @property isLoading True if the screen is currently processing an action, false otherwise.
     * @property pollQuestionError Error message for the poll question, if any.
     * @property pollOptionErrors List of error messages for each poll option, if any.
     * @property expiryDate Optional: Timestamp for poll expiry
     * @property showDatePickerDialog To control visibility of the date picker dialog
     * @property anonymous True if the poll is to be anonymous, false otherwise.
     * @property needsToFocusAfterValidation Flag to trigger focus logic in UI
     * @property showClearExpiryConfirmDialog For clearing expiry date confirmation
     * @property friendsList List of friends available for selection.
     * @property friendSelectionError Error message for friend selection, if any.
     * @property needsToFocusFriendError Flag to trigger focus logic for friend error
     */
    data class State(
        val pollQuestion: String = "",
        val numberOfOptions: Int = CreatePrivateTextPollDefaults.DEFAULT_NUMBER_OF_OPTIONS,
        val pollOptions: List<String> = List(CreatePrivateTextPollDefaults.DEFAULT_NUMBER_OF_OPTIONS) { "" },
        val isLoading: Boolean = false,
        val pollQuestionError: String? = null,
        val pollOptionErrors: List<String?> = List(CreatePrivateTextPollDefaults.DEFAULT_NUMBER_OF_OPTIONS) { null },
        val expiryDate: Long? = null,
        val showDatePickerDialog: Boolean = false,
        val anonymous: Boolean = false,
        val needsToFocusAfterValidation: Boolean = false,
        val showClearExpiryConfirmDialog: Boolean = false,
        val friendsList: List<FriendListItem> = emptyList(), // Initialize as empty
        val friendSelectionError: String? = null,
        val needsToFocusFriendError: Boolean = false // New flag for scrolling to friend error
    ) : ViewState

    /**
     * Represents the events that can be triggered by user interactions on the Create Private Text Poll screen.
     */
    sealed class Event : ViewEvent {
        /**
         * Event triggered when the poll question text changes.
         * @param question The new poll question.
         */
        data class OnPollQuestionChanged(val question: String) : Event()

        /**
         * Event triggered when the number of options changes.
         * @param count The new number of options.
         */
        data class OnNumberOfOptionsChanged(val count: Int) : Event()

        /**
         * Event triggered when the text of a specific poll option changes.
         * @param index The index of the poll option.
         * @param text The new text for the poll option.
         */
        data class OnPollOptionChanged(val index: Int, val text: String) : Event()

        /**
         * Event triggered when the "Create Poll" button is clicked.
         */
        data object OnCreatePollClicked : Event()

        /**
         * Event triggered when the user changes the expiry date.
         * @param dateMillis The selected date as milliseconds since epoch, or null if cleared.
         */
        data class OnExpiryDateChanged(val dateMillis: Long?) : Event()

        /**
         * Event triggered to show or hide the date picker dialog.
         */
        data object OnToggleDatePickerDialog : Event()

        /**
         * Event to clear the focus trigger flag after UI has handled it.
         */
        data object ClearFocusAfterValidationFlag : Event()

        /**
         * Event triggered when the clear expiry date icon is clicked (to show confirm dialog).
         */
        data object OnClearExpiryIconClicked : Event()
        
        /**
         * Event triggered to toggle the visibility of the clear expiry confirmation dialog.
         */
        data object OnToggleClearExpiryConfirmDialog : Event()

        /**
         * Event triggered when the user confirms clearing the expiry date.
         */
        data object OnConfirmClearExpiryDate : Event()

        /**
         * Event triggered when the poll anonymity selection changes (e.g., via RadioButton).
         * @param anonymous True if anonymous poll is selected, false otherwise.
         */
        data class OnPollAnonymityChanged(val anonymous: Boolean) : Event()

        /**
         * Event triggered when a friend is selected or deselected from the list.
         * @param friendId The ID of the friend.
         * @param isSelected The new selection state.
         */
        data class OnFriendSelected(val friendId: String, val isSelected: Boolean) : Event()

        /**
         * Event triggered when the "Select All" / "Deselect All" action is performed for friends.
         * @param selectAll True to select all, false to deselect all.
         */
        data class OnSelectAllFriendsClicked(val selectAll: Boolean) : Event()

        /**
         * Event to clear the friend error focus trigger flag after UI has handled it.
         */
        data object ClearFocusFriendErrorFlag : Event()
    }

    /**
     * Represents the side effects that can occur as a result of processing events on the Create Private Text Poll screen.
     * These are typically used for one-time actions like navigation or showing a Toast.
     */
    sealed class Effect : ViewEffect {
        /**
         * Effect to navigate to the previous screen or a success screen after poll creation.
         */
        data object NavigateBack : Effect() // Or NavigateToPollSuccess

        /**
         * Effect to show a generic error message.
         * @param message The error message to display.
         */
        data class ShowError(val message: String) : Effect()
    }
} 