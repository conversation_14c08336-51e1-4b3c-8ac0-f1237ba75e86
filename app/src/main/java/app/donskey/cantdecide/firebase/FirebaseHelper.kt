package app.donskey.cantdecide.firebase

import android.os.Bundle
import android.util.Log
import com.google.firebase.analytics.FirebaseAnalytics
import com.google.firebase.analytics.ktx.analytics
import com.google.firebase.auth.FirebaseAuth
import com.google.firebase.auth.FirebaseUser
import com.google.firebase.auth.FirebaseAuthInvalidUserException
import com.google.firebase.firestore.DocumentReference
import com.google.firebase.firestore.FirebaseFirestore
import com.google.firebase.firestore.ktx.firestore
import com.google.firebase.ktx.Firebase
import kotlinx.coroutines.tasks.await
import kotlinx.coroutines.delay

/**
 * Firebase Helper class that provides centralized access to Firebase services
 * and implements common Firebase operations.
 */
class FirebaseHelper {

    companion object {
        private const val TAG = "FirebaseHelper"

        // Firebase service instances - Use lazy initialization to avoid static context issues
        private val auth: FirebaseAuth by lazy { FirebaseAuth.getInstance() }
        private val firestore: FirebaseFirestore by lazy { Firebase.firestore }
        private val analytics: FirebaseAnalytics by lazy { Firebase.analytics }

        /**
         * Gets the current authenticated user.
         * @return FirebaseUser? The current user or null if not authenticated
         */
        fun getCurrentUser(): FirebaseUser? {
            return auth.currentUser
        }

        /**
         * Gets a reference to a specific user document in Firestore.
         * @param userId String the unique ID of the user
         * @return DocumentReference Reference to the user document in Firestore
         */
        fun getUserDocumentReference(userId: String): DocumentReference {
            return firestore.collection("users").document(userId)
        }

        /**
         * Logs an analytics event.
         * @param eventName String the name of the event to log
         * @param params Bundle? optional parameters to include with the event
         */
        fun logEvent(eventName: String, params: Bundle? = null) {
            analytics.logEvent(eventName, params)
            Log.d(TAG, "Logged analytics event: $eventName")
        }

        /**
         * Revalidates the current user's authentication token with Firebase.
         * This helps ensure the user hasn't been deleted on the server.
         * @return Boolean True if token refresh was successful, false otherwise
         */
        suspend fun revalidateAuthToken(): Boolean {
            val currentUser = auth.currentUser ?: return false

            return try {
                // Add a small delay to allow potential state propagation
                delay(500L)

                // Force a token refresh
                currentUser.getIdToken(true).await()
                Log.d(TAG, "Auth token revalidated successfully for user: ${currentUser.uid}")
                true
            } catch (e: Exception) {
                Log.e(TAG, "Auth token revalidation failed: ${e.message}", e)

                // Only sign out if the specific error indicates the user is invalid/deleted/disabled
                if (e is FirebaseAuthInvalidUserException) {
                    // Reset theme settings to defaults when signing out
                    app.donskey.cantdecide.ui.theme.ThemeManager.resetToDefaults()

                    auth.signOut()
                    Log.w(TAG, "User signed out due to invalid user state during token validation.")
                } else {
                    // For other errors (network, temporary issues), just log, don't sign out.
                    // The subsequent Firestore check in UserRepository will handle cases where the user doc is missing.
                    Log.w(TAG, "Token validation failed, but not signing out. Error: ${e.message}")
                }

                // Log the error to Analytics
                logEvent("auth_token_validation_error", Bundle().apply {
                    putString("error_message", e.message ?: "Unknown error")
                })

                false
            }
        }
    }
}