<?xml version="1.0" encoding="utf-8"?>
<resources>
    <!-- Base application theme -->
    <style name="AppTheme" parent="Theme.MaterialComponents.Light.DarkActionBar">
        <item name="colorPrimary">@color/primary</item>
        <item name="colorPrimaryDark">@color/primary_dark</item>
        <item name="colorAccent">@color/accent</item>
        <item name="android:windowBackground">@color/background</item>
        <item name="android:textColorPrimary">@color/text_primary</item>
        <item name="android:textColorSecondary">@color/text_secondary</item>
        <item name="materialButtonStyle">@style/AppButtonStyle</item>
    </style>
    
    <!-- No action bar theme variant for auth screens -->
    <style name="AppTheme.NoActionBar">
        <item name="windowActionBar">false</item>
        <item name="windowNoTitle">true</item>
        <item name="android:statusBarColor">@color/primary_dark</item>
    </style>
    
    <!-- Custom style for buttons -->
    <style name="AppButtonStyle" parent="Widget.MaterialComponents.Button">
        <item name="backgroundTint">@color/primary</item>
        <item name="android:textColor">@color/white</item>
        <item name="cornerRadius">8dp</item>
    </style>

    <!-- Custom style for the phone number input field -->
    <style name="PhoneInputStyle" parent="Widget.MaterialComponents.TextInputLayout.OutlinedBox">
        <item name="boxStrokeColor">@color/primary</item>
        <item name="boxBackgroundColor">@color/input_background</item>
        <item name="hintTextColor">@color/primary</item>
        <item name="android:textColorHint">@color/text_secondary</item>
        <item name="colorControlActivated">@color/primary</item>
        <item name="colorControlNormal">@color/text_secondary</item>
        <item name="boxCornerRadiusBottomEnd">8dp</item>
        <item name="boxCornerRadiusBottomStart">8dp</item>
        <item name="boxCornerRadiusTopEnd">8dp</item>
        <item name="boxCornerRadiusTopStart">8dp</item>
    </style>
</resources> 