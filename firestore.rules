rules_version = '2';

service cloud.firestore {
  match /databases/{database}/documents {
    // Function to check if the user is authenticated
    function isAuthenticated() {
      return request.auth != null;
    }

    // Function to check if the user is the owner of the resource
    function isOwner(userId) {
      return isAuthenticated() && request.auth.uid == userId;
    }

    // Users collection
    match /users/{userId} {
      // Allow ANY authenticated user to read ANY user profile.
      // This ensures the results of the phone number query can be read.
      // Make sure profile data is okay to be read by any logged-in user.
      allow read: if isAuthenticated();

      // Allow users to update only their own profile
      allow update: if isOwner(userId) && request.resource.data.displayName is string;

      // Allow users to create their own profile
      allow create: if isOwner(userId) && request.resource.data.phoneNumber is string;

      // Only allow deletion by the owner
      allow delete: if isOwner(userId);
    }

    // Add a rule for LISTING/QUERYING the users collection
    // Allow authenticated users to list/query users ONLY by phone number
    match /users/{document=**} { // This applies to the collection itself for queries
        allow list: if isAuthenticated() &&
                       // Ensure the query is ONLY filtering by phoneNumber
                       request.query.limit <= 1 && // Limit results (good practice for this query)
                       request.query.offset == null && // No offset allowed
                       // Check that the only filter field is 'phoneNumber'
                       request.query.filters.size() == 1 &&
                       request.query.filters[0].field == "phoneNumber" &&
                       request.query.filters[0].operator == "==";

       // Deny general writes to the collection path (writes happen via specific document match above)
       allow write: if false;
    }

    // Friends collection
    match /friends/{friendsId} {
      // Friends-specific helper functions
      function isInvolvedInFriends() {
        return isAuthenticated() &&
               (resource.data.userId == request.auth.uid ||
                resource.data.userId2 == request.auth.uid);
      }

      // Helper function for reading own outgoing pending requests
      function isInitiatorOfPendingRequest() {
        return isAuthenticated() &&
               resource.data.initiatedBy == request.auth.uid &&
               resource.data.status == 'pending';
      }

      function isCreatingValidFriends() {
        return isAuthenticated() &&
               (request.resource.data.userId == request.auth.uid ||
                request.resource.data.userId2 == request.auth.uid) &&
               request.resource.data.initiatedBy == request.auth.uid &&
               request.resource.data.status == "pending";
      }

      function isAcceptingOwnRequest() {
        return isAuthenticated() &&
               resource.data.status == "pending" &&
               request.resource.data.status == "accepted" &&
               ((resource.data.userId == request.auth.uid && resource.data.initiatedBy != request.auth.uid) ||
                (resource.data.userId2 == request.auth.uid && resource.data.initiatedBy != request.auth.uid));
      }

      function isRejectingOwnRequest() {
        return isAuthenticated() &&
               resource.data.status == "pending" &&
               request.resource.data.status == "rejected" &&
               ((resource.data.userId == request.auth.uid && resource.data.initiatedBy != request.auth.uid) ||
                (resource.data.userId2 == request.auth.uid && resource.data.initiatedBy != request.auth.uid));
      }

      function isCancelingOwnRequest() {
        return isAuthenticated() &&
               resource.data.status == "pending" &&
               request.resource.data.status == "canceled" &&
               resource.data.initiatedBy == request.auth.uid;
      }

      // Function to check if user is removing an accepted friend
      function isRemovingFriend() {
        return isAuthenticated() &&
               resource.data.status == "accepted" &&
               request.resource.data.status == "removed" &&
               // Ensure only the status field is being changed in this update
               request.resource.data.diff(resource.data).affectedKeys().hasOnly(['status']) && 
               isInvolvedInFriends(); // Either user can remove
      }

      // Read rule - Allow users to read friends they're part of OR their own outgoing pending requests
      allow read: if isInvolvedInFriends() || isInitiatorOfPendingRequest();

      // Create rule - Allow users to create friends they're part of
      allow create: if isCreatingValidFriends();

      // Update rule - Allow status changes following proper flow
      allow update: if isAcceptingOwnRequest() ||
                     isRejectingOwnRequest() ||
                     isCancelingOwnRequest() ||
                     isRemovingFriend() ||
                     (isInvolvedInFriends() &&
                      resource.data.status == "accepted" &&
                      request.resource.data.status == "accepted" &&
                      request.resource.data.notificationEnabled != resource.data.notificationEnabled);

      // Delete rule - Allow both users to delete an existing friendship
      allow delete: if isInvolvedInFriends();
    }

    // --- NEW Rules for Private Polls ---
    match /private_polls/{pollId} {
      // Helper to check if the poll being created/updated is correctly formed for a private poll
      function isWellFormedPrivatePollCreation() {
        return request.resource.data.creatorId == request.auth.uid &&
               request.resource.data.privacyLevel == "private" &&
               request.resource.data.question is string &&
               request.resource.data.options is list &&
               request.resource.data.anonymous is bool &&
               request.resource.data.type is string &&
               request.resource.data.createdAt is timestamp &&
               request.resource.data.status is string;
      }

      // Allow create if authenticated, user is creator, and it's correctly formed as private
      allow create: if isAuthenticated() && isWellFormedPrivatePollCreation();

      // Allow read if user is creator OR is in the allowedVoters list
      allow read: if isAuthenticated() &&
                   (resource.data.creatorId == request.auth.uid ||
                    (resource.data.allowedVoters is list && request.auth.uid in resource.data.allowedVoters));

      // Allow poll owners to update their polls.
      // Be specific about what fields can be updated and ensure core properties are not changed illicitly.
      allow update: if isAuthenticated() && resource.data.creatorId == request.auth.uid &&
                       // Prevent changing critical immutable fields
                       request.resource.data.creatorId == resource.data.creatorId &&
                       request.resource.data.privacyLevel == resource.data.privacyLevel && // Should remain "private"
                       request.resource.data.type == resource.data.type && // Type shouldn't change after creation
                       request.resource.data.createdAt == resource.data.createdAt; // Creation timestamp is immutable

      // Allow poll owners to delete their polls
      allow delete: if isAuthenticated() && resource.data.creatorId == request.auth.uid;

      // Votes subcollection for private polls
      match /votes/{voteId} {
        // User must be authenticated, the voterId in the vote document must be their own UID,
        // the pollId in the vote must match the parent poll, and the user must be allowed to read/access the parent poll.
        function canVoteOnPrivatePoll() {
          let pollDoc = get(/databases/$(database)/documents/private_polls/$(pollId));
          return pollDoc.data.creatorId == request.auth.uid ||
                 (pollDoc.data.allowedVoters is list && request.auth.uid in pollDoc.data.allowedVoters);
        }

        allow create: if isAuthenticated() &&
                         request.resource.data.voterId == request.auth.uid &&
                         request.resource.data.pollId == pollId &&
                         request.resource.data.optionId is string &&
                         request.resource.data.votedAt is timestamp &&
                         exists(/databases/$(database)/documents/private_polls/$(pollId)) &&
                         canVoteOnPrivatePoll();
        
        // Allow voter to read their own vote OR creator to read all votes for their poll
        allow read: if isAuthenticated() && 
                       (resource.data.voterId == request.auth.uid || 
                        get(/databases/$(database)/documents/private_polls/$(pollId)).data.creatorId == request.auth.uid);
                        
        // Allow voter to delete their own vote OR creator to delete any vote in their poll
        allow delete: if isAuthenticated() &&
                         (resource.data.voterId == request.auth.uid ||
                          get(/databases/$(database)/documents/private_polls/$(pollId)).data.creatorId == request.auth.uid); 
      }
    } // This closes match /private_polls/{pollId}

    // Rule for LISTING private_polls collection
    // This is essential for queries to work.
    match /private_polls/{document=**} {
      allow list: if isAuthenticated();
      // Add more specific conditions if needed, e.g.,
      // ensure queries only filter by creatorId or allowedVoters.
      // For now, allowing authenticated users to list, and relying on client-side queries
      // combined with the document-level read rules to enforce data visibility.
      // Firestore will only return documents from the list that the user also has read permission for.
    }
    // --- END NEW Rules for Private Polls ---

    // Public test collection for connectivity testing
    match /test/{testId} {
      allow read: if true; // Allow public read access for testing
      allow write: if false; // No public write access
    }

    // --- NEW Rules: Pending Email Verifications Collection ---
    match /pendingEmailVerifications/{email} {
      // Allow CREATE only if the user is authenticated and the userId in the 
      // document being created matches the authenticated user's UID.
      // Also ensure they are providing a valid timestamp and email.
      allow create: if isAuthenticated() && 
                       request.resource.data.userId == request.auth.uid &&
                       request.resource.data.requestedAt == request.time &&
                       request.resource.data.email is string && // Ensure email field is a string
                       request.resource.data.size() == 3; // Ensure only expected fields (userId, email, requestedAt)
                       
      // Allow READ by any authenticated user. Client-side logic will determine if it's for the current user or another.
      allow read: if isAuthenticated();
                     
      // Allow DELETE only if the user is authenticated and the userId in the
      // document being deleted matches the authenticated user's UID.
      allow delete: if isAuthenticated() && 
                       resource.data.userId == request.auth.uid;
                       
      // Allow user to update their OWN pending record (e.g., to update requestedAt).
      // The userId in the document must not change and must match the requesting user.
      // Email must be a string, and requestedAt must be a server timestamp.
      allow update: if isAuthenticated() &&
                       resource.data.userId == request.auth.uid && // Can only update their own record
                       request.resource.data.userId == resource.data.userId && // userId cannot be changed
                       request.resource.data.email is string &&
                       request.resource.data.requestedAt == request.time &&
                       request.resource.data.size() == 3; // Ensure only expected fields (userId, email, requestedAt)
    }
    // --- END NEW Rules ---
  }
}

// Define rules for Firebase Storage
service firebase.storage {
  match /b/{bucket}/o {
    // Match the user's folder (no trailing slash)
    match /users/{userId} {
      // Then match the specific profile image file within that folder
      match /profile.jpg {
        allow write: if request.auth != null && request.auth.uid == userId;
        // Allow anyone to read profile images (adjust if privacy needed)
        allow read: if true; 
      }
      // Add rules for other files within the user's folder here if needed
    }
    
    // Add other top-level storage rules here if needed
  }
}