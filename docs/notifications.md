Detailed Outline: App Notification System (Friend Request Example)

This system ensures notifications are handled consistently whether the app is in the foreground, background, or killed, and respects user preferences.

I. Cloud Function (functions/src/index.ts) - e.g., handleFriendRequestCreation

- Trigger Source:
  Listens to a specific backend event. For friend requests, it's onDocumentCreated("friends/{friendshipId}") when a new friend request document (status: "pending") appears in Firestore.  
   For other types: This would be different triggers, e.g., a new document in a "pollInvitations" collection, an update to a "votes" subcollection, etc.
- Core Responsibilities:
  Perform any necessary backend logic (e.g., updating user counts like pendingRequestCount).
  Identify the recipient user(s) for the notification.
  Fetch the recipient's data, specifically their FCM registration token and their in-app notification preference for this specific type of notification.
  Conditionally send an FCM message.
- FCM Message Construction & Sending:
  Conditional Send: Crucially, the function checks if the recipient has enabled notifications for this specific event type (e.g., recipientUserData.notificationSettings.notifyOnFriendRequest === true) AND has a valid FCM token.
- Payload Type:
  Constructs a DATA-ONLY FCM message. This is key to ensuring MyFirebaseMessagingService.onMessageReceived in the app is always called.
  token: The recipient user's FCM registration token.
  data: A map containing all necessary information for the app to process the notification:
  type: (String) A unique identifier for this notification category (e.g., "friend_request", "poll_invitation", "new_vote"). The app uses this to route handling.
  title: (String) The desired title for the notification (e.g., "New Friend Request").
  body: (String) The main message content (e.g., "\{senderName\} sent you a friend request!").
  senderName: (String, Optional) Display name of the initiating user, if applicable.
  Other relevant IDs: (e.g., friendshipId, pollId, commentId) to help the app navigate or fetch further details.
  android: (Object, Optional) Android-specific FCM options.
  priority: "high" (To help with timely delivery, especially on battery-optimized devices).
  Sending Mechanism: Uses admin.messaging().send(messagePayload).

II. Android App: Global State & Utilities

- Foreground/Background State Tracking (CantDecideApplication.kt):
  Dependency: androidx.lifecycle:lifecycle-process.
  The custom Application class (app.donskey.cantdecide.CantDecideApplication) implements LifecycleEventObserver.
  In its onCreate(), it registers itself as an observer to ProcessLifecycleOwner.get().lifecycle.
  A companion object property var isAppInForeground: Boolean = false (publicly readable, privately settable) is updated in onStateChanged based on Lifecycle.Event.ON_START and Lifecycle.Event.ON_STOP.
  This allows any part of the app, including services, to check the app's current foreground status.
  The custom application class is registered in AndroidManifest.xml.
  Global Coroutine Scope for Notifications (NotificationScope.kt):
  An object NotificationScope provides a singleton CoroutineScope(Dispatchers.IO + SupervisorJob()).
  This scope is used by MyFirebaseMessagingService for tasks (like fetching user settings from Firestore) that need to potentially outlive the service's own lifecycle, preventing premature cancellation, especially for background messages.
  Snackbar Manager (SnackbarMessageManager.kt):
  An object SnackbarMessageManager uses a MutableSharedFlow to allow any component (especially MyFirebaseMessagingService) to request a Snackbar to be shown.
  MainActivity collects this flow and displays the messages using its main SnackbarHostState with a Long duration.

III. Android App:

- FCM Message Handling (MyFirebaseMessagingService.kt)
  onMessageReceived(remoteMessage: RemoteMessage):
  This method is called whenever a data-only FCM message is received.
  Data Extraction: Retrieves type, title, body, senderName, and other custom data fields from remoteMessage.data.
- Notification Channel Creation:
  Calls createNotificationChannel() to ensure the necessary channel for this notification type exists (important for Android 8.0+).
- Routing Logic (based on data.type):
  If type == "friend_request" (or any other specific type):
  Always Post to Snackbar: Calls SnackbarMessageManager.showMessage(finalBody) so if the app is in the foreground, a Snackbar appears.
- User Authentication & Preference Check:
  Gets the current userId via FirebaseAuth.
- If User Logged In:
  Launches a coroutine in NotificationScope.scope.
  Fetches the user's full profile using userRepository.getUserProfile().
  Determines shouldNotify based on the specific preference (e.g., userProfile.notificationSettings.notifyOnFriendRequest). Defaults to true if the profile/setting can't be fetched (to err on the side of notifying).
- Conditional System Notification & Sound:
- If shouldNotify is true:
  If App is Background/Killed (!CantDecideApplication.isAppInForeground): Calls showFriendRequestNotification(finalTitle, finalBody). The system notification displayed through this function will use its channel's configured sound.
  If App is Foreground (CantDecideApplication.isAppInForeground): Skips calling showFriendRequestNotification() (to prevent duplicate visual notification) and explicitly calls playNotificationSound().
  If shouldNotify is false: No system notification is shown, and no explicit sound is played.
- If User Not Logged In:
  Launches a coroutine in NotificationScope.scope.
  If App is Background/Killed: Calls showFriendRequestNotification(finalTitle, finalBody).
  If App is Foreground: Skips showFriendRequestNotification() and explicitly calls playNotificationSound().
  else (for other future notification types):
  Similar logic would be implemented, checking its own specific type, user preference, and calling its own show<Type>Notification() function. Explicit sound play (playNotificationSound()) is kept here for now as a general fallback.
  show<Type>Notification(title: String, body: String) (e.g., showFriendRequestNotification)
  suspend function, typically uses withContext(Dispatchers.Main) for UI-related parts if any (though NotificationManagerCompat is safe to call from background threads).
  Intent Creation:
  Creates an Intent to launch MainActivity.
  Sets flags like Intent.FLAG_ACTIVITY_NEW_TASK or Intent.FLAG_ACTIVITY_CLEAR_TASK.
  Includes extras like putExtra("navigateTo", "friends") or more specific data (e.g., pollId) to tell MainActivity where to navigate or what content to load.
  PendingIntent Creation: Wraps the Intent in a PendingIntent for the notification's tap action.
  Notification Construction (NotificationCompat.Builder):
  setSmallIcon(): Uses an app-specific icon (e.g., R.drawable.ic_notification_icon - must exist).
  setContentTitle(title), setContentText(body).
  setChannelId(): Uses a unique channel ID for this notification type (e.g., FRIEND_REQUEST_CHANNEL_ID).
  setPriority(NotificationCompat.PRIORITY_HIGH).
  setContentIntent(pendingIntent).
  setAutoCancel(true).
  Displaying: Uses NotificationManagerCompat.from(this).notify(UNIQUE_NOTIFICATION_ID_INT, notificationBuilder.build()).
  UNIQUE_NOTIFICATION_ID_INT: An integer unique to this notification type (e.g., FRIEND_REQUEST_NOTIFICATION_ID = 1001). This allows updating or cancelling notifications of this type specifically.
  playNotificationSound():
  Simple utility to play the device's default notification sound using RingtoneManager.
  createNotificationChannel():
  Must be called before posting any notification on Android 8.0+ (API 26+).
  Creates a NotificationChannel for each type of notification if it doesn't already exist.
  Unique Channel ID: (e.g., "friend_requests_channel", "poll_invitations_channel"). This is the ID linked in NotificationCompat.Builder.
  User-Visible Name: (e.g., "Friend Requests", "Poll Invitations"). Shown in system settings.
  Importance Level: (e.g., NotificationManager.IMPORTANCE_HIGH). This affects how intrusive the notification is (sound, heads-up, etc.).
  Description: User-visible description.
  Sound: Channels with IMPORTANCE_DEFAULT or HIGH typically play sound by default. This is relied upon for background notifications.
  Registers the channel using NotificationManager.createNotificationChannel().
  IV. Android App: Data Models, Settings UI, and Navigation
  Data Models (User.kt):
  The User data class contains a nested NotificationSettings data class.
  NotificationSettings has a Boolean property for each notification type (e.g., notifyOnFriendRequest: Boolean = true, pollInvitations: Boolean = true), all defaulting to true. These are non-nullable.
  Settings UI (SettingsScreen.kt, SettingsViewModel.kt, UserRepository.kt):
  The "Notification Preferences" screen provides toggles (Switch composables) for each setting in NotificationSettings.
  SettingsViewModel loads these preferences via UserRepository and saves changes back to Firestore.
  Navigation from Notification (MainActivity.kt):
  onCreate() and onNewIntent() check the incoming Intent for extras like "navigateTo" (or more specific data like "pollId").
  Based on these extras, MainActivity (specifically its MainScreen composable, which hosts the NavController) navigates to the appropriate screen or tab (e.g., Friends tab, specific poll details screen).
  Adapting for New Notification Types (e.g., Poll Invitations):
  Cloud Function:
  Create a new trigger (e.g., onPollInvitationCreated).
  In its logic, send a data-only FCM message with type: "poll_invitation", relevant title, body, and pollId.
  User.kt: Add val pollInvitations: Boolean = true to NotificationSettings.
  Settings UI: Add a new toggle for "Poll Invitations".
  MyFirebaseMessagingService.kt:
  Add a new const val POLL_INVITATIONS_CHANNEL_ID = "poll_invitations_channel" and const val POLL_INVITATION_NOTIFICATION_ID = 1002.
  In createNotificationChannel(), add a section to create this new channel.
  In onMessageReceived(), add an else if (type == "poll_invitation") block with logic similar to friend_request:
  Post to Snackbar.
  Check userProfile.notificationSettings.pollInvitations.
  Conditionally call playNotificationSound() (for foreground) or a new showPollInvitationNotification() (for background).
  Create showPollInvitationNotification():
  Similar to showFriendRequestNotification but uses POLL_INVITATIONS_CHANNEL_ID, POLL_INVITATION_NOTIFICATION_ID.
  Its PendingIntent should carry data to navigate to the specific poll (e.g., putExtra("pollId", pollIdFromData)).
  MainActivity.kt: Extend handleIntentExtras and the LaunchedEffect in MainScreen to handle the new navigation target (e.g., if intent.getStringExtra("navigateTo") == "poll_details" and also get the pollId).
