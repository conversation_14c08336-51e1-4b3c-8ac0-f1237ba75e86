<!-- v1 -->

# Can't Decide - Current Session State

## 1. App Overview

**App Name:** Can't Decide

**Core Concept:** A social decision-making application designed to help users make choices by creating and sharing polls with friends, family, or a wider public audience.

**Key Features Implemented/Planned:**

- **Poll Management:**
  - Users can create private polls (shared with selected friends) and public polls.
  - Polls can consist of text-based questions and options. Image and video polls are future considerations.
  - For private polls, creators can specify which of their friends are allowed to vote.
- **Social Interaction:**
  - A friends system allows users to connect, send/receive friend requests, and manage friend lists.
  - Users can find friends by searching their phone contacts or by directly inputting a phone number.
- **User Authentication:**
  - Primary authentication is via phone number, similar to WhatsApp, utilizing Firebase Authentication.
- **Technology Stack:**
  - **Platform:** Android (Native)
  - **UI:** Jetpack Compose
  - **Backend & Database:** Firebase (Firestore for database, Firebase Authentication, Firebase Storage for images/videos, Firebase Cloud Functions for backend logic).
  - **Architecture:** Clean Architecture principles, MVI (Model-View-Intent) pattern for UI state management.
  - **Dependency Injection:** Hilt.
- **Navigation:**
  - Main navigation managed by a Bottom Navigation Bar (`MainActivity`).
  - Current poll-related navigation (creating, viewing, voting) is being consolidated under the "Polls" tab, leading to screens like "View Private Polls".

**Relevant File Paths (Examples to keep in mind):**

- Data Models: `app/src/main/java/app/donskey/cantdecide/data/model/poll_models.kt` (contains `PrivateTextPoll`, `PollOption`, `PollVote`)
- Polls Repository:
  - Interface: `app/src/main/java/app/donskey/cantdecide/domain/repository/PollsRepository.kt`
  - Implementation: `app/src/main/java/app/donskey/cantdecide/data/repository/PollsRepositoryImpl.kt`
- View Private Polls Screen: `app/src/main/java/app/donskey/cantdecide/ui/view_private_polls/ViewPrivatePollsScreen.kt`
- ViewModel for ^: `app/src/main/java/app/donskey/cantdecide/ui/view_private_polls/ViewPrivatePollsViewModel.kt`
- Project Docs: `docs/README.md`, `docs/tasksProgress.md`

## 2. Current Goal: Implement Core Voting Functionality (Phase 1)

The immediate objective is to allow users to vote on existing private text polls. This involves creating a poll details screen where users can see poll options and cast their vote. The vote should be saved to Firestore, and the UI should reflect whether the user has already voted.

## 3. Detailed Steps for Phase 1: Voting Implementation

**Phase 1.1: Data Model Enhancement**

- [✅] **1.1.1:** Open `app/src/main/java/app/donskey/cantdecide/data/model/poll_models.kt`.
- [✅] **1.1.2:** Locate the `PollOption` data class.
- [✅] **1.1.3:** Add the property `val voteCount: Int = 0` to the `PollOption` data class.
- [✅ ] **1.1.4:** Verify that the `PollVote` data class (already in `poll_models.kt`) is suitable:
  - `val voteId: String = ""`
  - `val pollId: String = ""`
  - `val voterId: String = ""`
  - `val optionId: String = ""`
  - `val votedAt: Timestamp = Timestamp(Date())`
- ✅ **1.1.5:** Build the project to ensure no compilation errors arise from these model changes.

**Phase 1.2: Update `PollsRepository` Interface**

- ✅ **1.2.1:** Open `app/src/main/java/app/donskey/cantdecide/domain/repository/PollsRepository.kt`.
- ✅ **1.2.2:** Add the following function signature for fetching detailed poll information:
  ```kotlin
  suspend fun getPollDetails(pollId: String): Resource<PrivateTextPoll>
  ```
- ✅ **1.2.3:** Add the following function signature for fetching a user's existing vote on a poll:
  ```kotlin
  suspend fun getUserVote(pollId: String, userId: String): Resource<PollVote?>
  ```
- ✅ **1.2.4:** Add the following function signature for submitting a new vote:
  ```kotlin
  suspend fun submitVote(vote: PollVote): Resource<Unit>
  ```
- ✅ **1.2.5:** Ensure all necessary import statements for `Resource`, `PrivateTextPoll`, and `PollVote` are present.
- ✅ **1.2.6:** Build the project to confirm the interface compiles without errors.

**Phase 1.3: Implement Repository Methods in `PollsRepositoryImpl`**

- ✅ **1.3.1:** Open `app/src/main/java/app/donskey/cantdecide/data/repository/PollsRepositoryImpl.kt`.
- ✅ **1.3.2:** Implement `getPollDetails(pollId: String)`:
  - ✅ 1.3.2.a: Use the existing `pollsCollection` (reference to `private_polls`).
  - ✅ 1.3.2.b: Fetch the document: `pollsCollection.document(pollId).get().await()`.
  - ✅ 1.3.2.c: Convert the `DocumentSnapshot` to a `PrivateTextPoll` object (e.g., `snapshot.toObject(PrivateTextPoll::class.java)`). Handle the case where the document might not exist or conversion fails.
  - ✅ 1.3.2.d: Return `Resource.Success(poll)` or `Resource.Error(message, null)`.
- ✅ **1.3.3:** Implement `getUserVote(pollId: String, userId: String)`:
  - ✅ 1.3.3.a: Define the path to the subcollection: `pollsCollection.document(pollId).collection("votes")`.
  - ✅ 1.3.3.b: Execute the query: `whereEqualTo("voterId", userId).limit(1).get().await()`.
  - ✅ 1.3.3.c: If the query result is not empty and a document exists, convert the first document to a `PollVote` object.
  - ✅ 1.3.3.d: Return `Resource.Success(voteObjectOrNull)` or `Resource.Error(message, null)`.
- ✅ **1.3.4:** Implement `submitVote(vote: PollVote)`:
  - ✅ 1.3.4.a: Define the path to the subcollection: `pollsCollection.document(vote.pollId).collection("votes")`.
  - ✅ 1.3.4.b: Determine the `voteId` to use. If `vote.voteId` is blank, generate a new one: `votesCollection.document().id`. Otherwise, use `vote.voteId`.
  - ✅ 1.3.4.c: Create/get the document reference: `votesCollection.document(actualVoteId)`.
  - ✅ 1.3.4.d: Set the data: `documentReference.set(vote.copy(voteId = actualVoteId)).await()`.
  - ✅ 1.3.4.e: Return `Resource.Success(Unit)` or `Resource.Error(message, null)`.
- ✅ **1.3.5:** Add all necessary import statements.
- ✅ **1.3.6:** Build the project. Run static analysis if available. _During manual testing of these functions later, if Firebase interactions fail, I will request a screenshot of Logcat output related to Firestore operations._

**Phase 1.4: Create `PollDetailsViewModel`**

- ✅ **1.4.1:** Create a new package: `app.donskey.cantdecide.ui.poll_details`.
- ✅ **1.4.2:** Create `PollDetailsViewModel.kt` file in the new package.
- ✅ **1.4.3:** Define the `PollDetailsUiState` data class:
  ```kotlin
  data class PollDetailsUiState(
      val isLoading: Boolean = true,
      val poll: PrivateTextPoll? = null,
      val userVote: PollVote? = null,
      val selectedOptionId: String? = null, // ID of the option the user clicks on before submitting
      val error: String? = null
  )
  ```
- ✅ **1.4.4:** Define the `PollDetailsEffect` sealed interface:
  ```kotlin
  sealed interface PollDetailsEffect {
      data object VoteSubmittedSuccessfully : PollDetailsEffect
      data class ShowError(val message: String) : PollDetailsEffect
      // Consider adding NavigateBack effect if needed, or handle in screen based on VoteSubmittedSuccessfully
  }
  ```
- ✅ **1.4.5:** Define the `PollDetailsEvent` sealed interface:
  ```kotlin
  sealed interface PollDetailsEvent {
      data class OptionSelected(val optionId: String) : PollDetailsEvent
      data object SubmitVoteClicked : PollDetailsEvent
  }
  ```
- ✅ **1.4.6:** Create the `PollDetailsViewModel` class:
  - ✅ 1.4.6.a: Annotate with `@HiltViewModel`.
  - ✅ 1.4.6.b: Inject `SavedStateHandle`, `PollsRepository`, and `UserRepository` via the constructor.
  - ✅ 1.4.6.c: Expose UI state: `private val _uiState = MutableStateFlow(PollDetailsUiState())`; `val uiState: StateFlow<PollDetailsUiState> = _uiState.asStateFlow()`.
  - ✅ 1.4.6.d: Expose effects: `private val _effect = MutableSharedFlow<PollDetailsEffect>()`; `val effect: SharedFlow<PollDetailsEffect> = _effect.asSharedFlow()`.
  - ✅ 1.4.6.e: Retrieve `pollId` from `SavedStateHandle` in an `init` block or a variable. Handle if `pollId` is null/missing (e.g., set error state).
- ✅ **1.4.7:** Implement data fetching logic in the `init` block (or called from it):
  - ✅ 1.4.7.a: Launch a coroutine to fetch poll details:
    - Set `_uiState.update { it.copy(isLoading = true) }`.
    - Call `pollsRepository.getPollDetails(pollId)`.
    - On `Resource.Success`, update `_uiState` with the poll data and `isLoading = false`.
    - On `Resource.Error`, update `_uiState` with the error message and `isLoading = false`.
  - ✅ 1.4.7.b: Launch a coroutine to fetch the user's existing vote:
    - Get `currentUserId` from `userRepository.getCurrentUser()?.uid`.
    - If `userId` is available, call `pollsRepository.getUserVote(pollId, userId)`.
    - On `Resource.Success`, update `_uiState` with `userVote`.
    - On `Resource.Error` (or if user not found), handle appropriately (e.g., log, `userVote` remains null).
- ✅ **1.4.8:** Implement the `handleEvent(event: PollDetailsEvent)` function:
  - ✅ 1.4.8.a: For `PollDetailsEvent.OptionSelected`: Update `_uiState.update { it.copy(selectedOptionId = event.optionId) }`.
  - ✅ 1.4.8.b: For `PollDetailsEvent.SubmitVoteClicked`:
    - Retrieve `currentSelectedOptionId = _uiState.value.selectedOptionId`, `currentPoll = _uiState.value.poll`, `currentUser = userRepository.getCurrentUser()`.
    - Perform checks: `selectedOptionId` not null, `poll` not null, `user` not null.
    - If checks pass:
      - Create a `PollVote` object (e.g., `voteId` blank for Firestore generation, `pollId = currentPoll!!.pollId`, `voterId = currentUser!!.uid`, `optionId = currentSelectedOptionId!!`, `votedAt = Timestamp.now()`).
      - Set `_uiState.update { it.copy(isLoading = true) }`.
      - Call `pollsRepository.submitVote(newVote)`.
      - On `Resource.Success`, emit `_effect.emit(PollDetailsEffect.VoteSubmittedSuccessfully)`. Also update `_uiState` with the new vote: `_uiState.update { it.copy(userVote = newVote, isLoading = false) }`.
      - On `Resource.Error`, emit `_effect.emit(PollDetailsEffect.ShowError(result.message ?: "Unknown error"))`. Set `_uiState.update { it.copy(isLoading = false) }`.
    - If checks fail, optionally log or show a specific error.
- ✅ **1.4.9:** Add all necessary import statements.
- ✅ **1.4.10:** Build the project and resolve any compilation errors.

**Phase 1.5: Create `PollDetailsScreen` UI**

- ✅ **1.5.1:** Create `PollDetailsScreen.kt` file in `app/donskey/cantdecide/ui/poll_details/`.
- ✅ **1.5.2:** Define the `PollDetailsScreen` Composable function:
  - Parameters: `viewModel: PollDetailsViewModel = hiltViewModel()`, `onNavigateBack: () -> Unit`, `onVoteSubmittedSuccessfullyAndNavigateBack: (pollIdVotedOn: String) -> Unit`.
- ✅ **1.5.3:** Collect UI state: `val uiState by viewModel.uiState.collectAsState()`.
- ✅ **1.5.4:** Handle side effects using `LaunchedEffect(Unit)` to collect `viewModel.effect`:
  - On `PollDetailsEffect.VoteSubmittedSuccessfully`: Call `onVoteSubmittedSuccessfullyAndNavigateBack(uiState.poll!!.pollId)`.
  - On `PollDetailsEffect.ShowError`: Display a `Snackbar` with `effect.message`. (Remember `SnackbarHostState`).
- ✅ **1.5.5:** Structure the UI using `Scaffold`:
  - ✅ 1.5.5.a: `TopAppBar`: Title (e.g., "Poll Details" or `uiState.poll?.question`), navigation icon (`Icons.AutoMirrored.Filled.ArrowBack`) with `onClick = onNavigateBack`.
  - ✅ 1.5.5.b: Content area (`Column` or `LazyColumn` in `paddingValues`):
    - If `uiState.isLoading` and `uiState.poll == null` (initial load), show `CircularProgressIndicator`.
    - If `uiState.error != null`, display the error message `Text(uiState.error)`.
    - If `uiState.poll != null`:
      - Display poll question: `Text(uiState.poll!!.question, style = MaterialTheme.typography.headlineSmall)`.
      - Iterate through `uiState.poll!!.options` to display each option:
        - Use a `Row` for each option.
        - `RadioButton`:
          - `selected = (option.optionId == uiState.selectedOptionId || option.optionId == uiState.userVote?.optionId)`.
          - `onClick = { if (uiState.userVote == null) viewModel.handleEvent(PollDetailsEvent.OptionSelected(option.optionId)) }`.
          - `enabled = (uiState.userVote == null)`.
        - `Text(option.text)` next to the `RadioButton`.
      - "Submit Vote" `Button` (or "You Voted" / "View Results"):
        - `text`: If `uiState.userVote != null`, show something like "You voted for: ${uiState.poll!!.options.find { it.optionId == uiState.userVote!!.optionId }?.text ?: ""}". Else, "Submit Vote".
        - `onClick = { viewModel.handleEvent(PollDetailsEvent.SubmitVoteClicked) }`.
        - `enabled = (uiState.selectedOptionId != null && uiState.userVote == null && !uiState.isLoading)`.
        - If `uiState.isLoading` (during submission), the button can show a `CircularProgressIndicator` inside or be disabled.
- ✅ **1.5.6:** Add necessary import statements.
- ✅ **1.5.7:** Create `@Preview` Composables for different UI states (loading, poll displayed, user already voted, error).
- ✅ **1.5.8:** Build project.

**Phase 1.6: Setup Navigation**

- ✅ **1.6.1:** Opened `MainActivity.kt` (where `NavHost` is defined).
- ✅ **1.6.2:** Used existing route constant `POLL_DETAILS_ROUTE` and argument `POLL_DETAILS_ARG_POLL_ID`.
- ✅ **1.6.3:** Added/verified `composable` entry for `POLL_DETAILS_ROUTE` in `NavHost`:
  ```kotlin
  composable(
      route = POLL_DETAILS_ROUTE,
      arguments = listOf(navArgument(POLL_DETAILS_ARG_POLL_ID) { type = NavType.StringType })
  ) {
      PollDetailsScreen(
          onNavigateBack = { navController.popBackStack() },
          onVoteSubmittedSuccessfullyAndNavigateBack = { votedPollId ->
              navController.previousBackStackEntry?.savedStateHandle?.set("votedPollId", votedPollId)
              navController.popBackStack()
          }
      )
  }
  ```
- ✅ **1.6.4:** In `ViewPrivatePollsScreen.kt`:
  - ✅ 1.6.4.a: Ensured `NavigateToPollDetails` effect in ViewModel leads to `navController.navigate("$POLL_DETAILS_ROUTE_BASE/${effect.pollId}")`.
  - ✅ 1.6.4.b: Added `LaunchedEffect` to listen for `navController.currentBackStackEntryAsState()` and retrieve `"votedPollId"` from `savedStateHandle`. If present, call `viewModel.handleEvent(ViewPrivatePollsEvent.UserVotedOnPoll(votedPollId))` and then remove the ID from `savedStateHandle`.
- ✅ **1.6.5:** Build project (debug build successful).

**Phase 1.7: Optimistic UI Update on "View Private Polls" Screen (For direct vote action from list)**

- ✅ **1.7.1:** Open `app/src/main/java/app/donskey/cantdecide/ui/view_private_polls/ViewPrivatePollsViewModel.kt`.
- ✅ **1.7.2:** Add `UserVotedOnPoll(val pollId: String)` event to `ViewPrivatePollsEvent` (in `ViewPrivatePollsContract.kt`).
- ✅ **1.7.3:** In `ViewPrivatePollsViewModel.handleEvent`, when `UserVotedOnPoll` is received, update the local `PollListItemUiModel` for the corresponding `pollId` to set `userHasVoted = true` (optimistic update).
- ✅ **1.7.4:** Build the project (debug build successful).

**Phase 1.8: Submit Vote to Firestore (From direct vote action on "View Private Polls" screen)**

- ✅ **1.8.1:** Update `ViewPrivatePollsViewModel.handleEvent` for `UserVotedOnPoll`:
  - ✅ 1.8.1.a: Get `currentUserId`.
  - ✅ 1.8.1.b: Construct `PollVote` object (using a placeholder `optionId` like `"dummy_option_id"` for now, as the direct list vote doesn't select a specific option from the list UI itself).
  - ✅ 1.8.1.c: Call `pollsRepository.submitVote(pollVote)`.
  - ✅ 1.8.1.d: Handle `Resource.Success` and `Resource.Error` (e.g., log, show toast via effect).
- ✅ **1.8.2:** Update `PollsRepository` interface and `PollsRepositoryImpl` if `submitVote` was not already suitable (it was, but ensure `PollVote` is constructed correctly in ViewModel).
  - The existing `submitVote(vote: PollVote)` in the repository was used.
  - `PollsRepositoryImpl.submitVote` saves the vote to `private_polls/{pollId}/votes/{voterId}`. It uses `FieldValue.serverTimestamp()` for `votedAt`.
- ✅ **1.8.3:** Ensure `PollVote` data class in `poll_models.kt` is correct (it was).
- ✅ **1.8.4:** Add missing import for `PollVote` in `ViewPrivatePollsViewModel.kt` if necessary.
- ✅ **1.8.5:** Build project (debug build successful).

**Phase 1.9: Implement Real-time Poll Updates (Vote Counts & Voted By Status)**

- ✅ **1.9.1: Update Data Model (`PrivateTextPoll`):**

  - ✅ 1.9.1.a: Open `app/src/main/java/app/donskey/cantdecide/data/model/poll_models.kt`.
  - ✅ 1.9.1.b: Add `val voteCount: Int = 0` to `PrivateTextPoll`.
  - ✅ 1.9.1.c: Add `val votedBy: List<String> = emptyList()` to `PrivateTextPoll` (to store user IDs of voters).
  - ✅ 1.9.1.d: Verified `PollListItemUiModel` in `ViewPrivatePollsContract.kt` already had `voteCount: Int`. `userHasVoted: Boolean` will be derived using the new `votedBy` list.

- ✅ **1.9.2: Develop Firebase Cloud Function (`updatePollVoteCounts`):**

  - ✅ 1.9.2.a: Edit `functions/src/index.ts`.
  - ✅ 1.9.2.b: Create a new Firestore trigger `updatePollVoteCounts = onDocumentWritten("private_polls/{pollId}/votes/{voteId}", ...)`. This function triggers when any document is written (created, updated, deleted) in the `votes` subcollection of any poll within `private_polls`.
  - ✅ 1.9.2.c: **Logic inside the function:**
    - Logs the trigger event.
    - **Crucially, checks if the event is a document creation**: `if (!event.data?.after.exists || event.data?.before.exists) { return; }`. This ensures counts are only updated for _new_ votes. Updates or deletions to existing vote documents in the subcollection will not trigger the count increment/voter addition (can be expanded later if vote changes/deletions need to decrement counts).
    - Retrieves `pollId` from `event.params` and `voterId` from the newly created vote document (`event.data.after.data()?.voterId`).
    - If `voterId` is valid, it references the parent poll document: `db.collection("private_polls").doc(pollId)`.
    - It then performs a **Firestore transaction** (`db.runTransaction`) on the parent poll document:
      - Reads the poll document.
      - If the poll document exists, it updates two fields:
        - `voteCount: admin.firestore.FieldValue.increment(1)` (atomically increments the vote count).
        - `votedBy: admin.firestore.FieldValue.arrayUnion(voterId)` (atomically adds the `voterId` to the `votedBy` array, ensuring no duplicates).
      - Logs success or error of the transaction.
  - ✅ 1.9.2.d: User needs to deploy this function via Firebase CLI: `firebase deploy --only functions`.

- ✅ **1.9.3: Update `PollsRepositoryImpl` and `PollsRepository` for Real-time Listening:**

  - ✅ 1.9.3.a: Verified `getPrivateTextPollsCreatedByCurrentUser` and `getPrivateTextPollsInvitedToCurrentUser` in `PollsRepositoryImpl.kt` already use `callbackFlow` with `addSnapshotListener`. These will automatically pick up changes to `PrivateTextPoll` documents (including the new `voteCount` and `votedBy` fields updated by the Cloud Function).
  - ✅ 1.9.3.b: Changed `getPollDetails(pollId: String)` in `PollsRepositoryImpl.kt` from a `suspend fun` returning `Resource<PrivateTextPoll>` to a `fun` returning `Flow<Resource<PrivateTextPoll?>>`. This uses `callbackFlow` and `addSnapshotListener` on a single document to provide real-time updates for the poll details screen.
  - ✅ 1.9.3.c: Updated the corresponding `getPollDetails` signature in the `PollsRepository.kt` interface.

- ✅ **1.9.4: Update `ViewPrivatePollsViewModel`:**

  - ✅ 1.9.4.a: In `transformToUiModel(poll: PrivateTextPoll, currentUserId: String)`:
    - Set `userHasVoted = poll.votedBy.contains(currentUserId)`.
    - Set `voteCount = poll.voteCount`.
    - Removed redundant Elvis operator `?: Timestamp.now()` from `createdAt = poll.createdAt` as `createdAt` is non-nullable.
  - ✅ 1.9.4.b: Restored avatar fetching logic in `transformToUiModel`, making it a `suspend fun` again. Updated the call site in `loadPolls` to use `allPolls.map { ... }` to handle the suspend transformation.
  - ✅ 1.9.4.c: Restored logic to display `creatorName` as "You" if the `poll.creatorId == currentUserId`.

- ✅ **1.9.5: Update `PollDetailsViewModel`:**

  - ✅ 1.9.5.a: Renamed `fetchPollDetails` to `observePollDetails`.
  - ✅ 1.9.5.b: `observePollDetails` now collects the `Flow<Resource<PrivateTextPoll?>>` from `pollsRepository.getPollDetails()`.
  - ✅ 1.9.5.c: When new `pollData` arrives in `observePollDetails`:
    - It updates `_uiState` with the latest `pollData` (including real-time `voteCount` and `votedBy`).
    - It checks if `currentUser.uid` is in `pollData.votedBy`. If yes, and `_uiState.value.userVote` isn't already set for this vote, it calls `fetchUserVote(pollId)` to get the specific `PollVote` object (to know which option was selected).
    - If `currentUser.uid` is not in `pollData.votedBy`, it clears `_uiState.update { it.copy(userVote = null) }`.

- ✅ **1.9.6: Build and Test:**
  - ✅ 1.9.6.a: Android application built successfully after addressing an "Unresolved reference: PollInfoRow" error in `ViewPrivatePollsScreen.kt` by re-applying the function definition and its calls.
  - ✅ 1.9.6.b: User tested: Real-time vote count updates confirmed working for multiple users. Avatar display and vote status text on list items also confirmed working.

**Phase 1.10: Polish and Refinements (Addressing test feedback)**

- ✅ **1.10.1: Adjust Vote Status Text on Poll List (`ViewPrivatePollsScreen.kt`):**

  - ✅ 1.10.1.a: Modified `PollListItemCard` to display vote status as:
    - "Votes: [count] (Voted)" if `poll.userHasVoted` is true.
    - "Votes: [count]" if `poll.userHasVoted` is false.
  - This was done by changing the text passed to the `PollInfoRow` for votes.

- ✅ **1.10.2: (Optional/Future) Display specific voted option on poll list:**
  - Current: List shows "(Voted)". Details screen shows the actual option.
  - To show option on list: Would require `PollListItemUiModel` to hold `userVotedOptionText: String?`. `ViewPrivatePollsViewModel.transformToUiModel` would need to fetch the specific `PollVote` for each poll the user has voted on. This could increase Firestore reads significantly if a user has voted on many polls in the list. Decided to keep current behavior for now.

## 4. Next Steps / Backlog Items

- **Option Selection on Details Screen:** Currently `dummy_option_id` is used when voting from the list. The `PollDetailsScreen` _should_ allow actual option selection. The `submitCurrentUserVote` in `PollDetailsViewModel` uses `_uiState.value.selectedOptionId`. We need to ensure the UI correctly populates this and the vote submission from details screen works end-to-end with the selected option.
- **Displaying Poll Results:** After voting, or if a poll is closed, users should see the results (e.g., vote counts per option, possibly percentages).
  - This might involve enhancing `PollOption` to store its individual `voteCount`.
  - A new Cloud Function might be needed to aggregate votes per option when a new vote comes in, updating `PollOption.voteCount` within the main `PrivateTextPoll` document.
- **Error Handling and Edge Cases:** Thoroughly test and improve error handling (e.g., network issues, Firestore permission errors).
- **UI/UX Polish:** Refine UI elements, screen transitions, and overall user experience based on Material 3 guidelines.
  - Address any remaining UI inconsistencies (like the avatar tint mentioned, if still relevant).
- **Delete Poll Functionality:** Implement the actual deletion logic for polls (currently just shows a snackbar).
- **Test `PollDetailsScreen` Voting:** The direct vote from `ViewPrivatePollsScreen` using `dummy_option_id` was for quick testing. The main voting flow via `PollDetailsScreen` (selecting a real option) needs full implementation and testing.

## Current Focus:

Testing the recent real-time updates and UI fixes.

Then, likely move to ensuring the **PollDetailsScreen allows selection of a real option, and `submitCurrentUserVote` in `PollDetailsViewModel` uses that selected optionId** instead of any dummy value.
