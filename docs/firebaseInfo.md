Package name: com.example.cant_decide

### Fingerprints(signatures)

MD5: 34:51:40:68:34:D4:A7:72:22:5D:F1:8C:65:63:A3:2B
SHA1: 1B:95:D3:6D:BE:9D:8A:A3:4A:A4:B2:A1:AC:C6:57:F9:46:B0:D0:1E
SHA1: 4d:af:6c:b8:1c:93:3e:a1:c7:5c:24:c4:b6:20:0b:2f:34:7b:04:22
SHA-256: 67:ED:6D:38:0C:6C:BB:60:35:4B:C3:1B:3B:C9:CF:99:24:23:C7:59:62:55:6C:B6:CD:9B:50:4E:69:3D:3D:54

Certificate fingerprints from Keystore
SHA1: 6E:C6:A0:D7:97:16:2B:AE:BA:55:56:D7:2C:C9:7C:98:0A:49:70:10
SHA256: E1:88:FC:55:1E:50:A4:A7:5B:BF:95:09:44:4C:8D:28:76:03:F2:FD:EC:38:E2:C8:2B:A4:9F:B4:ED:3C:76:81
PW: Tr741#
Alias: cant-decide-release

"My Dev Phone" Debug Token bf7ffb0f-b0d9-475b-afc2-2d86d0491c02

##########################################################
Package app.donskey.cantdecide
MD5: 6F:29:3D:2E:FC:62:75:1D:0C:AF:FF:13:9E:23:3E:4B
SHA1: 4D:AF:6C:B8:1C:93:3E:A1:C7:5C:24:C4:B6:20:0B:2F:34:7B:04:22
SHA-256: D5:FB:52:F0:AA:90:C3:C9:F8:E5:DE:87:6B:2C:57:16:36:A5:84:35:9F:8C:01:5D:BA:C9:96:F6:3C:26:4F:13
#########################################################
Privacy Policy Link
https://docs.google.com/document/d/e/2PACX-1vQlSuEccZAUeLFLMZRzvuK-PeCvYF5QA2JWG4hgpiVb_4M_Ew_Rv7rQDxvcdeSTs96L4wIY4sDs-Kqd/pub

Tester Link Version 2 1.1
https://play.google.com/apps/internaltest/4701678924725927943

### Installations

✅ Install Firebase CLI (See Instructions on notes document)

### Database Structure

**\*\*\*\*** Add the appropriate code to create User Data/Document to Firestore **\*\*\*\***

- **Users Collection**: `/users/{uid}`
  - ✅`allowTagging`: Boolean - true
  - ✅`bio`: String - Short bio about the user
  - ✅`createdAt`: Timestamp - When the user was created
  - ✅`decisionsCount`: Number - Number of decisions made
  - ✅`displayName`: String - Display name of the user
  - ✅`uid`: String - Users unique ID
  - ✅`email`: String - Email of the user (optional in user profile)
  - ✅`fcmToken`: String - Firebase Cloud Messaging token
  - ✅`friendsCount`: Number - Number of friends
  - ✅`lastActive`: Timestamp - When the user last logged in
  - ✅`notificationSettings`: Map
    - ✅ `appUpdates`:Boolean - True
    - ✅ `commentNotifications`:Boolean - True
    - ✅ `pollInvitations`:Boolean - True
    - ✅ `pollResults`:Boolean - True
    - ✅ `voteNotifications`:Boolean - True
  - ✅`pendingRequestCount`: Number - Number of pending friend requests
  - ✅`phoneNumber`: String - Phone number of the user
  - ✅`photoUrl`: String - URL of the user's profile photo
  - ✅`pollsCreated`: Number - Number of polls created
  - ✅`pollsVotedOn`: Number - Number of polls voted on
  - ✅`profileVisibility`: String - "public" - If user wants to hide without signing out
  - ✅`votesReceived`: Number - Number of votes received
  - ✅`themePreferences`: Map - Theme preferences (e.g. "light", "dark")
    - ✅`darkMode`: Boolean - false
    - ✅`themeColorIndex`: Number - 0
      -implement code to create a users document when a user is created in Firestore.

**\*\*\*\*** Add the appropriate code to create friendship Data/Document to Firestore **\*\*\*\***

- **Friends Collection**: `/friends/{friendsId}`
  - ✅`userId`: String - First user's Id
  - ✅`userId2`: String - Second user's Id
  - ✅`status`: String - Status of friendship ("pending", "accepted", "rejected")
  - ✅`initiatedBy`: String - User ID of who sent the request
  - ✅`phoneNumber`: String - Phone number of userId2 (optional, for invited users)
  - ✅`groupId`: String - Group identifier for future friend grouping (optional, nullable)
  - ✅`blocked`: Boolean - Whether the friendship is blocked (default: false)
  - ✅`createdAt`: Timestamp - When the request was sent
  - ✅`updatedAt`: Timestamp - When the status was last changed
  - ✅`lastInteraction`: Timestamp - When users last interacted
  - ✅`notificationEnabled`: Boolean - Whether notifications are enabled for this friendship
  - Create following composite indexes in Firestore, all ascending
    ✅ - (status, userId2, initiatedBy, _name_) - For incoming friend requests
    ✅ - (userId, status, _name_) - For getting friends where user is userId
    ✅ - (userId2, status, _name_) - For getting friends where user is userId2
    ✅ - (userId, status, initiatedBy, _name_) - For outgoing friend requests
    ✅ - (userId, userId2, _name_) - For user to user2 requests
    ✅ - (userId2, userId, _name_) - For user2 to user requests
    - The "_name_" is automatically included by Firestore in all composite indexes -
      it represents the document ID and helps with pagination and ordering, therefore
      do not include "_name_" when generating the composite indexes
      ✅ - Create all the appropriate cloud functions via Firebase CLI
      ✅ - Implement code to create a friendship document when a user sends a friend request
      in Firestore.

**\*\*\*\*** Add the appropriate code to create polls Data/Document to Firestore **\*\*\*\***

- **Polls Collection**: `/polls/{pollId}`
  - ✅`pollId`: String - Unique ID of the poll
  - ✅`title`: String - Title of poll
  - ✅`description`: String - Description of poll
  - ✅`imageURLs`: Array - Array of image URLs
    - 0 "https://example.com/image1.jpg"
    - 1 "https://example.com/image2.jpg"
  - ✅`options`: Array - String Array of decision options
    - 0 "Outfit1"
    - 1 "Outfil2"
  - ✅`userId`: String - Users unique ID
  - ✅`createdAt`: Timestamp - When the poll was created
  - ✅`expiresAt`: Timestamp - When the poll expires
  - ✅`visibility`: String - public or private
  - ✅`sharedWith`: Array of userIds of who can vote for private polls
  - ✅`status`: String - active or completed
  - implement code to create a poll document when a creates a poll in Firestore.

**\*\*\*\*** Add the appropriate code to create votes Data/Document to Firestore **\*\*\*\***

- **Votes Collection**: `/friendships/{friendshipId}`
  - ✅`voteId`: String - Unique ID of the vote (This should match the voteId in the subcollection
    for consistency)
  - ✅`pollId`: String - ID of the poll this vote belongs to (Links the vote to the parent poll)
  - ✅`userId`: String - ID of the user who voted
  - ✅`optionIndex`: Number - Index of the option the user voted for (Matches the field in
    the subcollection)
  - ✅`createdAt`: Timestamp - When the vote was cast
  - ✅`pollCreatorId`: String - ID of the user who created the poll

Suggestions (Discuss with claude and see what he thinks)

- Use Subcollection: Use /polls/{pollId}/votes/{voteId} for most operations, like calculating
  poll results and displaying real-time voting data. This is already set up and works well
  for poll-specific tasks.
- Add a Top-Level votes Collection: Use /votes/{voteId} to make it easier to query votes
  globally, especially for features like:
