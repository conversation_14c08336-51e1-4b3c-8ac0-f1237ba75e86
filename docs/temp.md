2025-05-26 16:37:07.293 9152-9152 AndroidRuntime app.donskey.cantdecide E FATAL EXCEPTION: main
Process: app.donskey.cantdecide, PID: 9152
java.lang.IllegalStateException: Vertically scrollable component was measured with an infinity maximum height constraints, which is disallowed. One of the common reasons is nesting layouts like LazyColumn and Column(Modifier.verticalScroll()). If you want to add a header before the list of items please add a header as a separate item() before the main items() inside the LazyColumn scope. There are could be other reasons for this to happen: your ComposeView was added into a LinearLayout with some weight, you applied Modifier.wrapContentSize(unbounded = true) or wrote a custom layout. Please try to remove the source of infinite constraints in the hierarchy above the scrolling container.
at androidx.compose.foundation.CheckScrollableContainerConstraintsKt.checkScrollableContainerConstraints-K40F9xA(CheckScrollableContainerConstraints.kt:35)
at androidx.compose.foundation.lazy.LazyListKt$rememberLazyListMeasurePolicy$1$1.invoke-0kLqBqw(LazyList.kt:181)
                                                                                                    	at androidx.compose.foundation.lazy.LazyListKt$rememberLazyListMeasurePolicy$1$1.invoke(LazyList.kt:178)
                                                                                                    	at androidx.compose.foundation.lazy.layout.LazyLayoutKt$LazyLayout$3$2$1.invoke-0kLqBqw(LazyLayout.kt:107)
                                                                                                    	at androidx.compose.foundation.lazy.layout.LazyLayoutKt$LazyLayout$3$2$1.invoke(LazyLayout.kt:100)
                                                                                                    	at androidx.compose.ui.layout.LayoutNodeSubcompositionsState$createMeasurePolicy$1.measure-3p2s80s(SubcomposeLayout.kt:709)
                                                                                                    	at androidx.compose.ui.node.InnerNodeCoordinator.measure-BRTryo0(InnerNodeCoordinator.kt:126)
                                                                                                    	at androidx.compose.foundation.AndroidOverscroll_androidKt$StretchOverscrollNonClippingLayer$2.invoke-3p2s80s(AndroidOverscroll.android.kt:584)
                                                                                                    	at androidx.compose.foundation.AndroidOverscroll_androidKt$StretchOverscrollNonClippingLayer$2.invoke(AndroidOverscroll.android.kt:583)
                                                                                                    	at androidx.compose.ui.layout.LayoutModifierImpl.measure-3p2s80s(LayoutModifier.kt:294)
                                                                                                    	at androidx.compose.ui.node.LayoutModifierNodeCoordinator.measure-BRTryo0(LayoutModifierNodeCoordinator.kt:116)
                                                                                                    	at androidx.compose.foundation.AndroidOverscroll_androidKt$StretchOverscrollNonClippingLayer$1.invoke-3p2s80s(AndroidOverscroll.android.kt:568)
                                                                                                    	at androidx.compose.foundation.AndroidOverscroll_androidKt$StretchOverscrollNonClippingLayer$1.invoke(AndroidOverscroll.android.kt:567)
                                                                                                    	at androidx.compose.ui.layout.LayoutModifierImpl.measure-3p2s80s(LayoutModifier.kt:294)
                                                                                                    	at androidx.compose.ui.node.LayoutModifierNodeCoordinator.measure-BRTryo0(LayoutModifierNodeCoordinator.kt:116)
                                                                                                    	at androidx.compose.ui.graphics.SimpleGraphicsLayerModifier.measure-3p2s80s(GraphicsLayerModifier.kt:646)
                                                                                                    	at androidx.compose.ui.node.LayoutModifierNodeCoordinator.measure-BRTryo0(LayoutModifierNodeCoordinator.kt:116)
                                                                                                    	at androidx.compose.ui.node.LayoutNodeLayoutDelegate$performMeasureBlock$1.invoke(LayoutNodeLayoutDelegate.kt:252)
                                                                                                    	at androidx.compose.ui.node.LayoutNodeLayoutDelegate$performMeasureBlock$1.invoke(LayoutNodeLayoutDelegate.kt:251)
                                                                                                    	at androidx.compose.runtime.snapshots.Snapshot$Companion.observe(Snapshot.kt:2303)
at androidx.compose.runtime.snapshots.SnapshotStateObserver$ObservedScopeMap.observe(SnapshotStateObserver.kt:500)
                                                                                                    	at androidx.compose.runtime.snapshots.SnapshotStateObserver.observeReads(SnapshotStateObserver.kt:256)
                                                                                                    	at androidx.compose.ui.node.OwnerSnapshotObserver.observeReads$ui*release(OwnerSnapshotObserver.kt:133)
at androidx.compose.ui.node.OwnerSnapshotObserver.observeMeasureSnapshotReads$ui_release(OwnerSnapshotObserver.kt:113)
                                                                                                    	at androidx.compose.ui.node.LayoutNodeLayoutDelegate.performMeasure-BRTryo0(LayoutNodeLayoutDelegate.kt:1617)
                                                                                                    	at androidx.compose.ui.node.LayoutNodeLayoutDelegate.access$performMeasure-BRTryo0(LayoutNodeLayoutDelegate.kt:36)
at androidx.compose.ui.node.LayoutNodeLayoutDelegate$MeasurePassDelegate.remeasure-BRTryo0(LayoutNodeLayoutDelegate.kt:620)
                                                                                                    	at androidx.compose.ui.node.LayoutNodeLayoutDelegate$MeasurePassDelegate.measure-BRTryo0(LayoutNodeLayoutDelegate.kt:596)
2025-05-26 16:37:07.294 9152-9152 AndroidRuntime app.donskey.cantdecide E at androidx.compose.foundation.layout.RowColumnMeasurementHelper.measureWithoutPlacing-\_EkL*-Y(RowColumnMeasurementHelper.kt:112)
at androidx.compose.foundation.layout.RowColumnMeasurePolicy.measure-3p2s80s(RowColumnImpl.kt:72)
at androidx.compose.ui.node.InnerNodeCoordinator.measure-BRTryo0(InnerNodeCoordinator.kt:126)
at androidx.compose.foundation.layout.PaddingNode.measure-3p2s80s(Padding.kt:397)
at androidx.compose.ui.node.LayoutModifierNodeCoordinator.measure-BRTryo0(LayoutModifierNodeCoordinator.kt:116)
at androidx.compose.ui.node.LayoutNodeLayoutDelegate$performMeasureBlock$1.invoke(LayoutNodeLayoutDelegate.kt:252)
                                                                                                    	at androidx.compose.ui.node.LayoutNodeLayoutDelegate$performMeasureBlock$1.invoke(LayoutNodeLayoutDelegate.kt:251)
                                                                                                    	at androidx.compose.runtime.snapshots.Snapshot$Companion.observe(Snapshot.kt:2303)
at androidx.compose.runtime.snapshots.SnapshotStateObserver$ObservedScopeMap.observe(SnapshotStateObserver.kt:500)
                                                                                                    	at androidx.compose.runtime.snapshots.SnapshotStateObserver.observeReads(SnapshotStateObserver.kt:256)
                                                                                                    	at androidx.compose.ui.node.OwnerSnapshotObserver.observeReads$ui*release(OwnerSnapshotObserver.kt:133)
at androidx.compose.ui.node.OwnerSnapshotObserver.observeMeasureSnapshotReads$ui_release(OwnerSnapshotObserver.kt:113)
                                                                                                    	at androidx.compose.ui.node.LayoutNodeLayoutDelegate.performMeasure-BRTryo0(LayoutNodeLayoutDelegate.kt:1617)
                                                                                                    	at androidx.compose.ui.node.LayoutNodeLayoutDelegate.access$performMeasure-BRTryo0(LayoutNodeLayoutDelegate.kt:36)
at androidx.compose.ui.node.LayoutNodeLayoutDelegate$MeasurePassDelegate.remeasure-BRTryo0(LayoutNodeLayoutDelegate.kt:620)
                                                                                                    	at androidx.compose.ui.node.LayoutNodeLayoutDelegate$MeasurePassDelegate.measure-BRTryo0(LayoutNodeLayoutDelegate.kt:596)
at androidx.compose.foundation.layout.RowColumnMeasurementHelper.measureWithoutPlacing-\_EkL*-Y(RowColumnMeasurementHelper.kt:112)
at androidx.compose.foundation.layout.RowColumnMeasurePolicy.measure-3p2s80s(RowColumnImpl.kt:72)
at androidx.compose.ui.node.InnerNodeCoordinator.measure-BRTryo0(InnerNodeCoordinator.kt:126)
at androidx.compose.ui.node.LayoutNodeLayoutDelegate$performMeasureBlock$1.invoke(LayoutNodeLayoutDelegate.kt:252)
                                                                                                    	at androidx.compose.ui.node.LayoutNodeLayoutDelegate$performMeasureBlock$1.invoke(LayoutNodeLayoutDelegate.kt:251)
                                                                                                    	at androidx.compose.runtime.snapshots.Snapshot$Companion.observe(Snapshot.kt:2303)
at androidx.compose.runtime.snapshots.SnapshotStateObserver$ObservedScopeMap.observe(SnapshotStateObserver.kt:500)
                                                                                                    	at androidx.compose.runtime.snapshots.SnapshotStateObserver.observeReads(SnapshotStateObserver.kt:256)
                                                                                                    	at androidx.compose.ui.node.OwnerSnapshotObserver.observeReads$ui_release(OwnerSnapshotObserver.kt:133)
at androidx.compose.ui.node.OwnerSnapshotObserver.observeMeasureSnapshotReads$ui_release(OwnerSnapshotObserver.kt:113)
                                                                                                    	at androidx.compose.ui.node.LayoutNodeLayoutDelegate.performMeasure-BRTryo0(LayoutNodeLayoutDelegate.kt:1617)
                                                                                                    	at androidx.compose.ui.node.LayoutNodeLayoutDelegate.access$performMeasure-BRTryo0(LayoutNodeLayoutDelegate.kt:36)
at androidx.compose.ui.node.LayoutNodeLayoutDelegate$MeasurePassDelegate.remeasure-BRTryo0(LayoutNodeLayoutDelegate.kt:620)
                                                                                                    	at androidx.compose.ui.node.LayoutNodeLayoutDelegate$MeasurePassDelegate.measure-BRTryo0(LayoutNodeLayoutDelegate.kt:596)
at androidx.compose.foundation.layout.BoxMeasurePolicy.measure-3p2s80s(Box.kt:122)
at androidx.compose.ui.node.InnerNodeCoordinator.measure-BRTryo0(InnerNodeCoordinator.kt:126)
at androidx.compose.ui.graphics.SimpleGraphicsLayerModifier.measure-3p2s80s(GraphicsLayerModifier.kt:646)
at androidx.compose.ui.node.LayoutModifierNodeCoordinator.measure-BRTryo0(LayoutModifierNodeCoordinator.kt:116)
at androidx.compose.ui.graphics.SimpleGraphicsLayerModifier.measure-3p2s80s(GraphicsLayerModifier.kt:646)
at androidx.compose.ui.node.LayoutModifierNodeCoordinator.measure-BRTryo0(LayoutModifierNodeCoordinator.kt:116)
at androidx.compose.foundation.layout.PaddingNode.measure-3p2s80s(Padding.kt:397)
2025-05-26 16:37:07.294 9152-9152 AndroidRuntime app.donskey.cantdecide E at androidx.compose.ui.node.LayoutModifierNodeCoordinator.measure-BRTryo0(LayoutModifierNodeCoordinator.kt:116)
at androidx.compose.foundation.layout.FillNode.measure-3p2s80s(Size.kt:699)
at androidx.compose.ui.node.LayoutModifierNodeCoordinator.measure-BRTryo0(LayoutModifierNodeCoordinator.kt:116)
at androidx.compose.ui.node.LayoutNodeLayoutDelegate$performMeasureBlock$1.invoke(LayoutNodeLayoutDelegate.kt:252)
                                                                                                    	at androidx.compose.ui.node.LayoutNodeLayoutDelegate$performMeasureBlock$1.invoke(LayoutNodeLayoutDelegate.kt:251)
                                                                                                    	at androidx.compose.runtime.snapshots.Snapshot$Companion.observe(Snapshot.kt:2303)
at androidx.compose.runtime.snapshots.SnapshotStateObserver$ObservedScopeMap.observe(SnapshotStateObserver.kt:500)
                                                                                                    	at androidx.compose.runtime.snapshots.SnapshotStateObserver.observeReads(SnapshotStateObserver.kt:256)
                                                                                                    	at androidx.compose.ui.node.OwnerSnapshotObserver.observeReads$ui_release(OwnerSnapshotObserver.kt:133)
at androidx.compose.ui.node.OwnerSnapshotObserver.observeMeasureSnapshotReads$ui_release(OwnerSnapshotObserver.kt:113)
                                                                                                    	at androidx.compose.ui.node.LayoutNodeLayoutDelegate.performMeasure-BRTryo0(LayoutNodeLayoutDelegate.kt:1617)
                                                                                                    	at androidx.compose.ui.node.LayoutNodeLayoutDelegate.access$performMeasure-BRTryo0(LayoutNodeLayoutDelegate.kt:36)
at androidx.compose.ui.node.LayoutNodeLayoutDelegate$MeasurePassDelegate.remeasure-BRTryo0(LayoutNodeLayoutDelegate.kt:620)
                                                                                                    	at androidx.compose.ui.node.LayoutNodeLayoutDelegate$MeasurePassDelegate.measure-BRTryo0(LayoutNodeLayoutDelegate.kt:596)
at androidx.compose.foundation.lazy.layout.LazyLayoutMeasureScopeImpl.measure-0kLqBqw(LazyLayoutMeasureScope.kt:127)
at androidx.compose.foundation.lazy.LazyListMeasuredItemProvider.getAndMeasure(LazyListMeasuredItemProvider.kt:48)
at androidx.compose.foundation.lazy.LazyListMeasureKt.measureLazyList-5IMabDg(LazyListMeasure.kt:195)
at androidx.compose.foundation.lazy.LazyListKt$rememberLazyListMeasurePolicy$1$1.invoke-0kLqBqw(LazyList.kt:313)
                                                                                                    	at androidx.compose.foundation.lazy.LazyListKt$rememberLazyListMeasurePolicy$1$1.invoke(LazyList.kt:178)
                                                                                                    	at androidx.compose.foundation.lazy.layout.LazyLayoutKt$LazyLayout$3$2$1.invoke-0kLqBqw(LazyLayout.kt:107)
                                                                                                    	at androidx.compose.foundation.lazy.layout.LazyLayoutKt$LazyLayout$3$2$1.invoke(LazyLayout.kt:100)
                                                                                                    	at androidx.compose.ui.layout.LayoutNodeSubcompositionsState$createMeasurePolicy$1.measure-3p2s80s(SubcomposeLayout.kt:709)
                                                                                                    	at androidx.compose.ui.node.InnerNodeCoordinator.measure-BRTryo0(InnerNodeCoordinator.kt:126)
                                                                                                    	at androidx.compose.foundation.AndroidOverscroll_androidKt$StretchOverscrollNonClippingLayer$2.invoke-3p2s80s(AndroidOverscroll.android.kt:584)
                                                                                                    	at androidx.compose.foundation.AndroidOverscroll_androidKt$StretchOverscrollNonClippingLayer$2.invoke(AndroidOverscroll.android.kt:583)
                                                                                                    	at androidx.compose.ui.layout.LayoutModifierImpl.measure-3p2s80s(LayoutModifier.kt:294)
                                                                                                    	at androidx.compose.ui.node.LayoutModifierNodeCoordinator.measure-BRTryo0(LayoutModifierNodeCoordinator.kt:116)
                                                                                                    	at androidx.compose.foundation.AndroidOverscroll_androidKt$StretchOverscrollNonClippingLayer$1.invoke-3p2s80s(AndroidOverscroll.android.kt:568)
                                                                                                    	at androidx.compose.foundation.AndroidOverscroll_androidKt$StretchOverscrollNonClippingLayer$1.invoke(AndroidOverscroll.android.kt:567)
                                                                                                    	at androidx.compose.ui.layout.LayoutModifierImpl.measure-3p2s80s(LayoutModifier.kt:294)
                                                                                                    	at androidx.compose.ui.node.LayoutModifierNodeCoordinator.measure-BRTryo0(LayoutModifierNodeCoordinator.kt:116)
                                                                                                    	at androidx.compose.ui.graphics.SimpleGraphicsLayerModifier.measure-3p2s80s(GraphicsLayerModifier.kt:646)
                                                                                                    	at androidx.compose.ui.node.LayoutModifierNodeCoordinator.measure-BRTryo0(LayoutModifierNodeCoordinator.kt:116)
                                                                                                    	at androidx.compose.foundation.layout.PaddingNode.measure-3p2s80s(Padding.kt:397)
                                                                                                    	at androidx.compose.ui.node.LayoutModifierNodeCoordinator.measure-BRTryo0(LayoutModifierNodeCoordinator.kt:116)
                                                                                                    	at androidx.compose.foundation.layout.FillNode.measure-3p2s80s(Size.kt:699)
2025-05-26 16:37:07.295  9152-9152  AndroidRuntime          app.donskey.cantdecide               E  	at androidx.compose.ui.node.LayoutModifierNodeCoordinator.measure-BRTryo0(LayoutModifierNodeCoordinator.kt:116)
                                                                                                    	at androidx.compose.ui.node.LayoutNodeLayoutDelegate$performMeasureBlock$1.invoke(LayoutNodeLayoutDelegate.kt:252)
                                                                                                    	at androidx.compose.ui.node.LayoutNodeLayoutDelegate$performMeasureBlock$1.invoke(LayoutNodeLayoutDelegate.kt:251)
                                                                                                    	at androidx.compose.runtime.snapshots.Snapshot$Companion.observe(Snapshot.kt:2303)
at androidx.compose.runtime.snapshots.SnapshotStateObserver$ObservedScopeMap.observe(SnapshotStateObserver.kt:500)
                                                                                                    	at androidx.compose.runtime.snapshots.SnapshotStateObserver.observeReads(SnapshotStateObserver.kt:256)
                                                                                                    	at androidx.compose.ui.node.OwnerSnapshotObserver.observeReads$ui_release(OwnerSnapshotObserver.kt:133)
at androidx.compose.ui.node.OwnerSnapshotObserver.observeMeasureSnapshotReads$ui_release(OwnerSnapshotObserver.kt:113)
                                                                                                    	at androidx.compose.ui.node.LayoutNodeLayoutDelegate.performMeasure-BRTryo0(LayoutNodeLayoutDelegate.kt:1617)
                                                                                                    	at androidx.compose.ui.node.LayoutNodeLayoutDelegate.access$performMeasure-BRTryo0(LayoutNodeLayoutDelegate.kt:36)
at androidx.compose.ui.node.LayoutNodeLayoutDelegate$MeasurePassDelegate.remeasure-BRTryo0(LayoutNodeLayoutDelegate.kt:620)
                                                                                                    	at androidx.compose.ui.node.LayoutNode.remeasure-_Sx5XlM$ui_release(LayoutNode.kt:1145)
at androidx.compose.ui.node.LayoutNode.remeasure-\_Sx5XlM$ui_release$default(LayoutNode.kt:1136)
at androidx.compose.ui.node.MeasureAndLayoutDelegate.doRemeasure-sdFAvZA(MeasureAndLayoutDelegate.kt:356)
at androidx.compose.ui.node.MeasureAndLayoutDelegate.remeasureAndRelayoutIfNeeded(MeasureAndLayoutDelegate.kt:514)
at androidx.compose.ui.node.MeasureAndLayoutDelegate.remeasureAndRelayoutIfNeeded$default(MeasureAndLayoutDelegate.kt:491)
                                                                                                    	at androidx.compose.ui.node.MeasureAndLayoutDelegate.measureAndLayout(MeasureAndLayoutDelegate.kt:377)
                                                                                                    	at androidx.compose.ui.platform.AndroidComposeView.measureAndLayout(AndroidComposeView.android.kt:971)
                                                                                                    	at androidx.compose.ui.node.Owner.measureAndLayout$default(Owner.kt:228)
at androidx.compose.ui.platform.AndroidComposeView.dispatchDraw(AndroidComposeView.android.kt:1224)
at android.view.View.draw(View.java:25180)
at android.view.View.updateDisplayListIfDirty(View.java:24036)
at android.view.ViewGroup.recreateChildDisplayList(ViewGroup.java:4764)
at android.view.ViewGroup.dispatchGetDisplayList(ViewGroup.java:4736)
at android.view.View.updateDisplayListIfDirty(View.java:23982)
at android.view.ViewGroup.recreateChildDisplayList(ViewGroup.java:4764)
at android.view.ViewGroup.dispatchGetDisplayList(ViewGroup.java:4736)
at android.view.View.updateDisplayListIfDirty(View.java:23982)
at android.view.ViewGroup.recreateChildDisplayList(ViewGroup.java:4764)
at android.view.ViewGroup.dispatchGetDisplayList(ViewGroup.java:4736)
at android.view.View.updateDisplayListIfDirty(View.java:23982)
at android.view.ViewGroup.recreateChildDisplayList(ViewGroup.java:4764)
at android.view.ViewGroup.dispatchGetDisplayList(ViewGroup.java:4736)
at android.view.View.updateDisplayListIfDirty(View.java:23982)
at android.view.ThreadedRenderer.updateViewTreeDisplayList(ThreadedRenderer.java:768)
at android.view.ThreadedRenderer.updateRootDisplayList(ThreadedRenderer.java:774)
at android.view.ThreadedRenderer.draw(ThreadedRenderer.java:872)
at android.view.ViewRootImpl.draw(ViewRootImpl.java:6051)
at android.view.ViewRootImpl.performDraw(ViewRootImpl.java:5706)
at android.view.ViewRootImpl.performTraversals(ViewRootImpl.java:4795)
at android.view.ViewRootImpl.doTraversal(ViewRootImpl.java:3288)
at android.view.ViewRootImpl$TraversalRunnable.run(ViewRootImpl.java:11344)
                                                                                                    	at android.view.Choreographer$CallbackRecord.run(Choreographer.java:1690)
at android.view.Choreographer$CallbackRecord.run(Choreographer.java:1699)
                                                                                                    	at android.view.Choreographer.doCallbacks(Choreographer.java:1154)
                                                                                                    	at android.view.Choreographer.doFrame(Choreographer.java:1080)
2025-05-26 16:37:07.295  9152-9152  AndroidRuntime          app.donskey.cantdecide               E  	at android.view.Choreographer$FrameDisplayEventReceiver.run(Choreographer.java:1647)
at android.os.Handler.handleCallback(Handler.java:958)
at android.os.Handler.dispatchMessage(Handler.java:99)
at android.os.Looper.loopOnce(Looper.java:230)
at android.os.Looper.loop(Looper.java:319)
at android.app.ActivityThread.main(ActivityThread.java:8934)
at java.lang.reflect.Method.invoke(Native Method)
at com.android.internal.os.RuntimeInit$MethodAndArgsCaller.run(RuntimeInit.java:588)
                                                                                                    	at com.android.internal.os.ZygoteInit.main(ZygoteInit.java:1103)
2025-05-26 16:37:07.590  9152-9152  Process                 app.donskey.cantdecide               I  Sending signal. PID: 9152 SIG: 9
---------------------------- PROCESS ENDED (9152) for package app.donskey.cantdecide ----------------------------
---------------------------- PROCESS STARTED (9444) for package app.donskey.cantdecide ----------------------------
2025-05-26 16:37:15.180  9444-9444  ziparchive              app.donskey.cantdecide               W  Unable to open '/data/app/~~Y2W9lc4p6WmutiSxF8gWRQ==/app.donskey.cantdecide-mht4cUmWkZ9Jj87N72I2kQ==/base.dm': No such file or directory
2025-05-26 16:37:15.180  9444-9444  ziparchive              app.donskey.cantdecide               W  Unable to open '/data/app/~~Y2W9lc4p6WmutiSxF8gWRQ==/app.donskey.cantdecide-mht4cUmWkZ9Jj87N72I2kQ==/base.dm': No such file or directory
2025-05-26 16:37:15.849  9444-9444  nativeloader            app.donskey.cantdecide               D  Configuring clns-4 for other apk /data/app/~~Y2W9lc4p6WmutiSxF8gWRQ==/app.donskey.cantdecide-mht4cUmWkZ9Jj87N72I2kQ==/base.apk. target_sdk_version=35, uses_libraries=, library_path=/data/app/~~Y2W9lc4p6WmutiSxF8gWRQ==/app.donskey.cantdecide-mht4cUmWkZ9Jj87N72I2kQ==/lib/arm64, permitted_path=/data:/mnt/expand:/data/user/0/app.donskey.cantdecide
2025-05-26 16:37:15.862  9444-9444  nativeloader            app.donskey.cantdecide               D  Load libframework-connectivity-jni.so using APEX ns com_android_tethering for caller /apex/com.android.tethering/javalib/framework-connectivity.jar: ok
2025-05-26 16:37:15.874  9444-9444  GraphicsEnvironment     app.donskey.cantdecide               V  Currently set values for:
2025-05-26 16:37:15.874  9444-9444  GraphicsEnvironment     app.donskey.cantdecide               V    angle_gl_driver_selection_pkgs=[]
2025-05-26 16:37:15.874  9444-9444  GraphicsEnvironment     app.donskey.cantdecide               V    angle_gl_driver_selection_values=[]
2025-05-26 16:37:15.875  9444-9444  GraphicsEnvironment     app.donskey.cantdecide               V  ANGLE GameManagerService for app.donskey.cantdecide: false
2025-05-26 16:37:15.875  9444-9444  GraphicsEnvironment     app.donskey.cantdecide               V  app.donskey.cantdecide is not listed in per-application setting
2025-05-26 16:37:15.876  9444-9444  GraphicsEnvironment     app.donskey.cantdecide               V  Neither updatable production driver nor prerelease driver is supported.
2025-05-26 16:37:15.982  9444-9444  Compatibil...geReporter app.donskey.cantdecide               D  Compat change id reported: 183155436; UID 10321; state: ENABLED
2025-05-26 16:37:16.005  9444-9444  SessionsDependencies    app.donskey.cantdecide               D  Dependency to CRASHLYTICS added.
2025-05-26 16:37:16.013  9444-9444  FirebaseApp             app.donskey.cantdecide               I  Device unlocked: initializing all Firebase APIs for app [DEFAULT]
2025-05-26 16:37:16.123  9444-9444  FirebaseSessions        app.donskey.cantdecide               D  Initializing Firebase Sessions SDK.
2025-05-26 16:37:16.128  9444-9444  FirebaseCrashlytics     app.donskey.cantdecide               I  Initializing Firebase Crashlytics 18.6.3 for app.donskey.cantdecide
2025-05-26 16:37:16.167  9444-9444  SessionsDependencies    app.donskey.cantdecide               D  Subscriber CRASHLYTICS registered.
2025-05-26 16:37:16.198  9444-9470  skey.cantdecide         app.donskey.cantdecide               W  ClassLoaderContext classpath size mismatch. expected=1, found=0 (DLC[];PCL[base.apk*109608243]{PCL[/system/framework/org.apache.http.legacy.jar***********]#PCL[/system/framework/com.android.media.remotedisplay.jar***********]#PCL[/system/framework/com.android.location.provider.jar*825940897]#PCL[/system/framework/org.apache.http.legacy.jar***********]} | DLC[];PCL[])
2025-05-26 16:37:16.201  9444-9470  DynamiteModule          app.donskey.cantdecide               I  Considering local module com.google.android.gms.measurement.dynamite:105 and remote module com.google.android.gms.measurement.dynamite:147
2025-05-26 16:37:16.201  9444-9470  DynamiteModule          app.donskey.cantdecide               I  Selected remote version of com.google.android.gms.measurement.dynamite, version >= 147
2025-05-26 16:37:16.201  9444-9470  DynamiteModule          app.donskey.cantdecide               V  Dynamite loader version >= 2, using loadModule2NoCrashUtils
2025-05-26 16:37:16.219  9444-9470  System                  app.donskey.cantdecide               W  ClassLoader referenced unknown path: 
2025-05-26 16:37:16.219  9444-9470  nativeloader            app.donskey.cantdecide               D  Configuring clns-5 for other apk . target_sdk_version=36, uses_libraries=, library_path=/data/app/~~S9ubu0yca15qASjyNNIVkg==/com.google.android.gms-_KALIfJ8k-83OpavkHtiow==/lib/arm64:/data/app/~~S9ubu0yca15qASjyNNIVkg==/com.google.android.gms-_KALIfJ8k-83OpavkHtiow==/base.apk!/lib/arm64-v8a, permitted_path=/data:/mnt/expand:/data/user/0/com.google.android.gms
2025-05-26 16:37:16.231  9444-9470  skey.cantdecide         app.donskey.cantdecide               W  ClassLoaderContext classpath element checksum mismatch. expected=109608243, found=********** (DLC[];PCL[base.apk*109608243]{PCL[/system/framework/org.apache.http.legacy.jar***********]#PCL[/system/framework/com.android.media.remotedisplay.jar***********]#PCL[/system/framework/com.android.location.provider.jar*825940897]#PCL[/system/framework/org.apache.http.legacy.jar***********]} | DLC[];PCL[/data/app/~~Y2W9lc4p6WmutiSxF8gWRQ==/app.donskey.cantdecide-mht4cUmWkZ9Jj87N72I2kQ==/base.apk***********])
2025-05-26 16:37:16.255  9444-9444  Compatibil...geReporter app.donskey.cantdecide               D  Compat change id reported: 3400644; UID 10321; state: ENABLED
2025-05-26 16:37:16.266  9444-9444  FirebaseInitProvider    app.donskey.cantdecide               I  FirebaseApp initialization successful
2025-05-26 16:37:16.269  9444-9471  LifecycleServiceBinder  app.donskey.cantdecide               D  Binding service to application.
2025-05-26 16:37:16.305  9444-9444  CantDecideApp_DEBUG     app.donskey.cantdecide               I  Application onCreate started
2025-05-26 16:37:16.306  9444-9444  CantDecideApp_DEBUG     app.donskey.cantdecide               I  FirebaseApp initialized successfully.
2025-05-26 16:37:16.306  9444-9444  CantDecideApp_DEBUG     app.donskey.cantdecide               D  Initializing App Check with DEBUG provider.
2025-05-26 16:37:16.312  9444-9474  com.google...ckProvider app.donskey.cantdecide               D  Enter this debug secret into the allow list in the Firebase Console for your project: e0f877b2-b0c8-463b-b05a-919824dfa294
2025-05-26 16:37:16.352  9444-9486  skey.cantdecide         app.donskey.cantdecide               E  No package ID 6d found for resource ID 0x6d0b000f.
2025-05-26 16:37:16.354  9444-9486  FA                      app.donskey.cantdecide               I  App measurement initialized, version: 125000
2025-05-26 16:37:16.354  9444-9486  FA                      app.donskey.cantdecide               I  To enable debug logging run: adb shell setprop log.tag.FA VERBOSE
2025-05-26 16:37:16.355  9444-9486  FA                      app.donskey.cantdecide               I  To enable faster debug mode event logging run:
                                                                                                      adb shell setprop debug.firebase.analytics.app app.donskey.cantdecide
2025-05-26 16:37:16.379  9444-9472  FirebaseCrashlytics     app.donskey.cantdecide               I  No version control information found
2025-05-26 16:37:16.401  9444-9444  FirebaseAuth            app.donskey.cantdecide               D  Notifying id token listeners about user ( MDIFs5fCHggRUd34MBUHNZGSehR2 ).
2025-05-26 16:37:16.406  9444-9444  CantDecideApp_DEBUG     app.donskey.cantdecide               I  Firebase Auth persistence check complete
2025-05-26 16:37:16.406  9444-9464  CantDecideApp_DEBUG     app.donskey.cantdecide               D  Refreshing auth token for persistence
2025-05-26 16:37:16.407  9444-9444  CantDecideApp_DEBUG     app.donskey.cantdecide               D  Auth instance: com.google.firebase.auth.internal.zzad@230aebe
2025-05-26 16:37:16.407  9444-9466  CantDecideApp_DEBUG     app.donskey.cantdecide               D  Checking Firestore existence for user: MDIFs5fCHggRUd34MBUHNZGSehR2 in Application
2025-05-26 16:37:16.408  9444-9444  CantDecideApp_DEBUG     app.donskey.cantdecide               D  Current user: MDIFs5fCHggRUd34MBUHNZGSehR2
2025-05-26 16:37:16.408  9444-9444  CantDecideApp_DEBUG     app.donskey.cantdecide               I  Application onCreate finished
2025-05-26 16:37:16.422  9444-9444  SessionLifecycleService app.donskey.cantdecide               D  Service bound to new client on process 9444
2025-05-26 16:37:16.427  9444-9477  System                  app.donskey.cantdecide               W  Ignoring header X-Firebase-Locale because its value was null.
2025-05-26 16:37:16.428  9444-9500  SessionLifecycleService app.donskey.cantdecide               D  App has not yet foregrounded. Using previously stored session: null
2025-05-26 16:37:16.428  9444-9500  SessionLifecycleService app.donskey.cantdecide               D  Client android.os.Messenger@59bd722 bound at 111727953. Clients: 1
2025-05-26 16:37:16.430  9444-9444  CantDecideApp_DEBUG     app.donskey.cantdecide               I  Auth state: SIGNED IN, UID: MDIFs5fCHggRUd34MBUHNZGSehR2, Email: <EMAIL>
2025-05-26 16:37:16.446  9444-9444  SessionLifecycleClient  app.donskey.cantdecide               D  Connected to SessionLifecycleService. Queue size 0
2025-05-26 16:37:16.524  9444-9478  TRuntime.C...ortBackend app.donskey.cantdecide               I  Making request to: https://crashlyticsreports-pa.googleapis.com/v1/firelog/legacy/batchlog
2025-05-26 16:37:16.545  9444-9512  DynamiteModule          app.donskey.cantdecide               W  Local module descriptor class for com.google.android.gms.providerinstaller.dynamite not found.
2025-05-26 16:37:16.552  9444-9512  DynamiteModule          app.donskey.cantdecide               I  Considering local module com.google.android.gms.providerinstaller.dynamite:0 and remote module com.google.android.gms.providerinstaller.dynamite:0
2025-05-26 16:37:16.552  9444-9512  ProviderInstaller       app.donskey.cantdecide               W  Failed to load providerinstaller module: No acceptable module com.google.android.gms.providerinstaller.dynamite found. Local version is 0 and remote version is 0.
2025-05-26 16:37:16.563  9444-9512  nativeloader            app.donskey.cantdecide               D  Configuring clns-6 for other apk /system/framework/org.apache.http.legacy.jar. target_sdk_version=36, uses_libraries=ALL, library_path=/data/app/~~S9ubu0yca15qASjyNNIVkg==/com.google.android.gms-_KALIfJ8k-83OpavkHtiow==/lib/arm64:/data/app/~~S9ubu0yca15qASjyNNIVkg==/com.google.android.gms-_KALIfJ8k-83OpavkHtiow==/base.apk!/lib/arm64-v8a, permitted_path=/data:/mnt/expand:/data/user/0/com.google.android.gms
2025-05-26 16:37:16.563  9444-9512  nativeloader            app.donskey.cantdecide               D  Extending system_exposed_libraries: libface_landmark.arcsoft.so:liblow_light_hdr.arcsoft.so:libhigh_dynamic_range.arcsoft.so:libFacialAttributeDetection.arcsoft.so:libaudiomirroring_jni.audiomirroring.samsung.so:libBeauty_v4.camera.samsung.so:libexifa.camera.samsung.so:libjpega.camera.samsung.so:libOpenCv.camera.samsung.so:libImageScreener.camera.samsung.so:libMyFilter.camera.samsung.so:libtensorflowLite.myfilter.camera.samsung.so:libtensorflowlite_inference_api.myfilter.camera.samsung.so:libFace_Landmark_API.camera.samsung.so:libHpr_RecGAE_cvFeature_v1.0.camera.samsung.so:libHprFace_GAE_api.camera.samsung.so:libFacialBasedSelfieCorrection.camera.samsung.so:libHprFace_GAE_jni.camera.samsung.so:libcolor_engine.camera.samsung.so:libDLInterface_aidl.camera.samsung.so:libObjectAndSceneClassification_2.5_OD.camera.samsung.so:libSceneDetector_v1.camera.samsung.so:libQREngine.camera.samsung.so:libEventDetector.camera.samsung.so:libFood.camera.samsung.so:libFoodDetector.camera.samsung.so:libPortraitSolution.camera.s
2025-05-26 16:37:16.572  9444-9512  nativeloader            app.donskey.cantdecide               D  Configuring clns-7 for other apk /system/framework/com.android.media.remotedisplay.jar. target_sdk_version=36, uses_libraries=ALL, library_path=/data/app/~~S9ubu0yca15qASjyNNIVkg==/com.google.android.gms-_KALIfJ8k-83OpavkHtiow==/lib/arm64:/data/app/~~S9ubu0yca15qASjyNNIVkg==/com.google.android.gms-_KALIfJ8k-83OpavkHtiow==/base.apk!/lib/arm64-v8a, permitted_path=/data:/mnt/expand:/data/user/0/com.google.android.gms
2025-05-26 16:37:16.572  9444-9512  nativeloader            app.donskey.cantdecide               D  Extending system_exposed_libraries: libface_landmark.arcsoft.so:liblow_light_hdr.arcsoft.so:libhigh_dynamic_range.arcsoft.so:libFacialAttributeDetection.arcsoft.so:libaudiomirroring_jni.audiomirroring.samsung.so:libBeauty_v4.camera.samsung.so:libexifa.camera.samsung.so:libjpega.camera.samsung.so:libOpenCv.camera.samsung.so:libImageScreener.camera.samsung.so:libMyFilter.camera.samsung.so:libtensorflowLite.myfilter.camera.samsung.so:libtensorflowlite_inference_api.myfilter.camera.samsung.so:libFace_Landmark_API.camera.samsung.so:libHpr_RecGAE_cvFeature_v1.0.camera.samsung.so:libHprFace_GAE_api.camera.samsung.so:libFacialBasedSelfieCorrection.camera.samsung.so:libHprFace_GAE_jni.camera.samsung.so:libcolor_engine.camera.samsung.so:libDLInterface_aidl.camera.samsung.so:libObjectAndSceneClassification_2.5_OD.camera.samsung.so:libSceneDetector_v1.camera.samsung.so:libQREngine.camera.samsung.so:libEventDetector.camera.samsung.so:libFood.camera.samsung.so:libFoodDetector.camera.samsung.so:libPortraitSolution.camera.s
2025-05-26 16:37:16.580  9444-9512  skey.cantdecide         app.donskey.cantdecide               W  Loading /data/misc/apexdata/com.android.art/dalvik-cache/arm64/system@<EMAIL>@classes.odex non-executable as it requires an image which we failed to load
2025-05-26 16:37:16.585  9444-9512  nativeloader            app.donskey.cantdecide               D  Configuring clns-8 for other apk /system/framework/com.android.location.provider.jar. target_sdk_version=36, uses_libraries=ALL, library_path=/data/app/~~S9ubu0yca15qASjyNNIVkg==/com.google.android.gms-_KALIfJ8k-83OpavkHtiow==/lib/arm64:/data/app/~~S9ubu0yca15qASjyNNIVkg==/com.google.android.gms-_KALIfJ8k-83OpavkHtiow==/base.apk!/lib/arm64-v8a, permitted_path=/data:/mnt/expand:/data/user/0/com.google.android.gms
2025-05-26 16:37:16.585  9444-9512  nativeloader            app.donskey.cantdecide               D  Extending system_exposed_libraries: libface_landmark.arcsoft.so:liblow_light_hdr.arcsoft.so:libhigh_dynamic_range.arcsoft.so:libFacialAttributeDetection.arcsoft.so:libaudiomirroring_jni.audiomirroring.samsung.so:libBeauty_v4.camera.samsung.so:libexifa.camera.samsung.so:libjpega.camera.samsung.so:libOpenCv.camera.samsung.so:libImageScreener.camera.samsung.so:libMyFilter.camera.samsung.so:libtensorflowLite.myfilter.camera.samsung.so:libtensorflowlite_inference_api.myfilter.camera.samsung.so:libFace_Landmark_API.camera.samsung.so:libHpr_RecGAE_cvFeature_v1.0.camera.samsung.so:libHprFace_GAE_api.camera.samsung.so:libFacialBasedSelfieCorrection.camera.samsung.so:libHprFace_GAE_jni.camera.samsung.so:libcolor_engine.camera.samsung.so:libDLInterface_aidl.camera.samsung.so:libObjectAndSceneClassification_2.5_OD.camera.samsung.so:libSceneDetector_v1.camera.samsung.so:libQREngine.camera.samsung.so:libEventDetector.camera.samsung.so:libFood.camera.samsung.so:libFoodDetector.camera.samsung.so:libPortraitSolution.camera.s
2025-05-26 16:37:16.595  9444-9512  skey.cantdecide         app.donskey.cantdecide               W  Loading /data/app/~~S9ubu0yca15qASjyNNIVkg==/com.google.android.gms-_KALIfJ8k-83OpavkHtiow==/oat/arm64/base.odex non-executable as it requires an image which we failed to load
2025-05-26 16:37:16.607  9444-9512  nativeloader            app.donskey.cantdecide               D  Configuring clns-9 for other apk /data/app/~~S9ubu0yca15qASjyNNIVkg==/com.google.android.gms-_KALIfJ8k-83OpavkHtiow==/base.apk. target_sdk_version=36, uses_libraries=, library_path=/data/app/~~S9ubu0yca15qASjyNNIVkg==/com.google.android.gms-_KALIfJ8k-83OpavkHtiow==/lib/arm64:/data/app/~~S9ubu0yca15qASjyNNIVkg==/com.google.android.gms-_KALIfJ8k-83OpavkHtiow==/base.apk!/lib/arm64-v8a, permitted_path=/data:/mnt/expand:/data/user/0/com.google.android.gms
2025-05-26 16:37:16.612  9444-9512  ProviderInstaller       app.donskey.cantdecide               W  Failed to report request stats: com.google.android.gms.common.security.ProviderInstallerImpl.reportRequestStats [class android.content.Context, long, long]
2025-05-26 16:37:16.625  9444-9507  ConnectivityManager     app.donskey.cantdecide               D  StackLog: [android.net.ConnectivityManager.sendRequestForNetwork(ConnectivityManager.java:4709)] [android.net.ConnectivityManager.registerDefaultNetworkCallbackForUid(ConnectivityManager.java:5433)] [android.net.ConnectivityManager.registerDefaultNetworkCallback(ConnectivityManager.java:5400)] [android.net.ConnectivityManager.registerDefaultNetworkCallback(ConnectivityManager.java:5374)] [com.google.firebase.firestore.remote.AndroidConnectivityMonitor.configureNetworkMonitoring(AndroidConnectivityMonitor.java:87)] [com.google.firebase.firestore.remote.AndroidConnectivityMonitor.<init>(AndroidConnectivityMonitor.java:64)] [com.google.firebase.firestore.core.MemoryComponentProvider.createConnectivityMonitor(MemoryComponentProvider.java:68)] [com.google.firebase.firestore.core.MemoryComponentProvider.createConnectivityMonitor(MemoryComponentProvider.java:42)] [com.google.firebase.firestore.core.ComponentProvider.initialize(ComponentProvider.java:152)] [com.google.firebase.firestore.core.FirestoreClient.initialize(FirestoreClient.java:296)] [com.google.firebase.firestore.core.FirestoreClient.lambda$new$0$com-google-firebase-firestore-core-FirestoreClient(FirestoreClient.java:114)] [com.google.firebase.firestore.core.FirestoreClient$$ExternalSyntheticLambda6.run(D8$$SyntheticClass:0)] [com.google.firebase.firestore.util.AsyncQueue.lambda$enqueue$2(AsyncQueue.java:444)] [com.google.firebase.firestore.util.AsyncQueue$$ExternalSyntheticLambda4.call(D8$$SyntheticClass:0)] [com.google.firebase.firestore.util.AsyncQueue$SynchronizedShutdownAwareExecutor.lambda$executeAndReportResult$1(AsyncQueue.java:330)] [com.google.firebase.firestore.util.AsyncQueue$SynchronizedShutdownAwareExecutor$$ExternalSyntheticLambda2.run(D8$$SyntheticClass:0)] [java.util.concurrent.Executors$RunnableAdapter.call(Executors.java:487)] [java.util.concurrent.FutureTask.run(FutureTask.java:264)] [java.util.concurrent.ScheduledThreadPoolExecutor$ScheduledFutureTask.run(ScheduledThreadPoolExecutor.java:307)] [java.util.concurrent.ThreadPoolExecutor.runWorker(ThreadPoolExecutor.java:1145)] [java.util.concurrent.ThreadPoolExecutor$Worker.run(ThreadPoolExecutor.java:644)] [com.google.firebase.firestore.util.AsyncQueue$SynchronizedShutdownAwareExecutor$DelayedStartFactory.run(AsyncQueue.java:235)] [java.lang.Thread.run(Thread.java:1012)]
2025-05-26 16:37:16.641 9444-9522 skey.cantdecide app.donskey.cantdecide W Accessing hidden method Ldalvik/system/VMStack;->getStackClass2()Ljava/lang/Class; (unsupported, reflection, allowed)
2025-05-26 16:37:16.655 9444-9521 GoogleApiManager app.donskey.cantdecide E Failed to get service from broker.
java.lang.SecurityException: Unknown calling package name 'com.google.android.gms'.
at android.os.Parcel.createExceptionOrNull(Parcel.java:3091)
at android.os.Parcel.createException(Parcel.java:3075)
at android.os.Parcel.readException(Parcel.java:3058)
at android.os.Parcel.readException(Parcel.java:3000)
at axma.a(:com.google.android.gms@251833029@25.18.33 (190400-756823100):36)
at axkh.z(:com.google.android.gms@251833029@25.18.33 (190400-756823100):143)
at awrm.run(:com.google.android.gms@251833029@25.18.33 (190400-756823100):42)
at android.os.Handler.handleCallback(Handler.java:958)
at android.os.Handler.dispatchMessage(Handler.java:99)
at cgbo.mJ(:com.google.android.gms@251833029@25.18.33 (190400-756823100):1)
at cgbo.dispatchMessage(:com.google.android.gms@251833029@25.18.33 (190400-756823100):5)
at android.os.Looper.loopOnce(Looper.java:230)
at android.os.Looper.loop(Looper.java:319)
at android.os.HandlerThread.run(HandlerThread.java:67)
2025-05-26 16:37:16.663 9444-9518 FlagRegistrar app.donskey.cantdecide W Failed to register com.google.android.gms.providerinstaller#app.donskey.cantdecide
fdvz: 17: 17: API: Phenotype.API is not available on this device. Connection failed with: ConnectionResult{statusCode=DEVELOPER_ERROR, resolution=null, message=null}
at fdwb.a(:com.google.android.gms@251833029@25.18.33 (190400-756823100):13)
at fyzr.d(:com.google.android.gms@251833029@25.18.33 (190400-756823100):3)
at fyzt.run(:com.google.android.gms@251833029@25.18.33 (190400-756823100):130)
at fzca.execute(:com.google.android.gms@251833029@25.18.33 (190400-756823100):1)
at fzab.f(:com.google.android.gms@251833029@25.18.33 (190400-756823100):1)
at fzab.m(:com.google.android.gms@251833029@25.18.33 (190400-756823100):99)
at fzab.r(:com.google.android.gms@251833029@25.18.33 (190400-756823100):17)
at ewgv.hV(:com.google.android.gms@251833029@25.18.33 (190400-756823100):35)
at eknw.run(:com.google.android.gms@251833029@25.18.33 (190400-756823100):12)
at fzca.execute(:com.google.android.gms@251833029@25.18.33 (190400-756823100):1)
at eknx.b(:com.google.android.gms@251833029@25.18.33 (190400-756823100):18)
at ekom.b(:com.google.android.gms@251833029@25.18.33 (190400-756823100):36)
at ekoo.d(:com.google.android.gms@251833029@25.18.33 (190400-756823100):25)
at awou.c(:com.google.android.gms@251833029@25.18.33 (190400-756823100):9)
at awrk.q(:com.google.android.gms@251833029@25.18.33 (190400-756823100):48)
at awrk.d(:com.google.android.gms@251833029@25.18.33 (190400-756823100):10)
at awrk.g(:com.google.android.gms@251833029@25.18.33 (190400-756823100):185)
at awrk.onConnectionFailed(:com.google.android.gms@251833029@25.18.33 (190400-756823100):2)
at awrm.run(:com.google.android.gms@251833029@25.18.33 (190400-756823100):70)
at android.os.Handler.handleCallback(Handler.java:958)
at android.os.Handler.dispatchMessage(Handler.java:99)
at cgbo.mJ(:com.google.android.gms@251833029@25.18.33 (190400-756823100):1)
at cgbo.dispatchMessage(:com.google.android.gms@251833029@25.18.33 (190400-756823100):5)
at android.os.Looper.loopOnce(Looper.java:230)
at android.os.Looper.loop(Looper.java:319)
at android.os.HandlerThread.run(HandlerThread.java:67)
Caused by: awnj: 17: API: Phenotype.API is not available on this device. Connection failed with: ConnectionResult{statusCode=DEVELOPER_ERROR, resolution=null, message=null}
at axjt.a(:com.google.android.gms@251833029@25.18.33 (190400-756823100):15)
at awox.a(:com.google.android.gms@251833029@25.18.33 (190400-756823100):1)
at awou.c(:com.google.android.gms@251833029@25.18.33 (190400-756823100):5)
at awrk.q(:com.google.android.gms@251833029@25.18.33 (190400-756823100):48) 
at awrk.d(:com.google.android.gms@251833029@25.18.33 (190400-756823100):10) 
at awrk.g(:com.google.android.gms@251833029@25.18.33 (190400-756823100):185) 
at awrk.onConnectionFailed(:com.google.android.gms@251833029@25.18.33 (190400-756823100):2) 
at awrm.run(:com.google.android.gms@251833029@25.18.33 (190400-756823100):70) 
at android.os.Handler.handleCallback(Handler.java:958) 
at android.os.Handler.dispatchMessage(Handler.java:99) 
at cgbo.mJ(:com.google.android.gms@251833029@25.18.33 (190400-756823100):1) 
at cgbo.dispatchMessage(:com.google.android.gms@251833029@25.18.33 (190400-756823100):5) 
at android.os.Looper.loopOnce(Looper.java:230) 
at android.os.Looper.loop(Looper.java:319) 
at android.os.HandlerThread.run(HandlerThread.java:67) 
2025-05-26 16:37:17.132 9444-9512 nativeloader app.donskey.cantdecide D Load /data/app/~~S9ubu0yca15qASjyNNIVkg==/com.google.android.gms-\_KALIfJ8k-83OpavkHtiow==/base.apk!/lib/arm64-v8a/libconscrypt_gmscore_jni.so using ns clns-9 from class loader (caller=/data/app/~~S9ubu0yca15qASjyNNIVkg==/com.google.android.gms-\_KALIfJ8k-83OpavkHtiow==/base.apk): ok
2025-05-26 16:37:17.137 9444-9512 NativeCrypto app.donskey.cantdecide V Registering com/google/android/gms/org/conscrypt/NativeCrypto's 315 native methods...
2025-05-26 16:37:17.161 9444-9512 skey.cantdecide app.donskey.cantdecide W Accessing hidden method Ljava/security/spec/ECParameterSpec;->getCurveName()Ljava/lang/String; (unsupported, reflection, allowed)
2025-05-26 16:37:17.234 9444-9512 ProviderInstaller app.donskey.cantdecide I Installed default security provider GmsCore_OpenSSL
2025-05-26 16:37:17.348 9444-9512 ConnectivityManager app.donskey.cantdecide D StackLog: [android.net.ConnectivityManager.sendRequestForNetwork(ConnectivityManager.java:4709)] [android.net.ConnectivityManager.registerDefaultNetworkCallbackForUid(ConnectivityManager.java:5433)] [android.net.ConnectivityManager.registerDefaultNetworkCallback(ConnectivityManager.java:5400)] [android.net.ConnectivityManager.registerDefaultNetworkCallback(ConnectivityManager.java:5374)] [io.grpc.android.AndroidChannelBuilder$AndroidChannel.configureNetworkMonitoring(AndroidChannelBuilder.java:216)] [io.grpc.android.AndroidChannelBuilder$AndroidChannel.<init>(AndroidChannelBuilder.java:197)] [io.grpc.android.AndroidChannelBuilder.build(AndroidChannelBuilder.java:168)] [com.google.firebase.firestore.remote.GrpcCallProvider.initChannel(GrpcCallProvider.java:116)] [com.google.firebase.firestore.remote.GrpcCallProvider.lambda$initChannelTask$6$com-google-firebase-firestore-remote-GrpcCallProvider(GrpcCallProvider.java:242)] [com.google.firebase.firestore.remote.GrpcCallProvider$$ExternalSyntheticLambda4.call(D8$$SyntheticClass:0)] [com.google.android.gms.tasks.zzz.run(com.google.android.gms:play-services-tasks@@18.1.0:1)] [com.google.firebase.firestore.util.ThrottledForwardingExecutor.lambda$execute$0$com-google-firebase-firestore-util-ThrottledForwardingExecutor(ThrottledForwardingExecutor.java:54)] [com.google.firebase.firestore.util.ThrottledForwardingExecutor$$ExternalSyntheticLambda0.run(D8$$SyntheticClass:0)] [java.util.concurrent.ThreadPoolExecutor.runWorker(ThreadPoolExecutor.java:1145)] [java.util.concurrent.ThreadPoolExecutor$Worker.run(ThreadPoolExecutor.java:644)] [java.lang.Thread.run(Thread.java:1012)]
2025-05-26 16:37:17.461 9444-9537 skey.cantdecide app.donskey.cantdecide W Accessing hidden field Ljava/net/Socket;->impl:Ljava/net/SocketImpl; (unsupported, reflection, allowed)
2025-05-26 16:37:17.606 9444-9537 skey.cantdecide app.donskey.cantdecide W Accessing hidden method Ljava/security/spec/ECParameterSpec;->setCurveName(Ljava/lang/String;)V (unsupported, reflection, allowed)
2025-05-26 16:37:17.720 9444-9477 LocalRequestInterceptor app.donskey.cantdecide W Error getting App Check token; using placeholder token instead. Error: com.google.firebase.FirebaseException: Error returned from API. code: 403 body: App attestation failed.
2025-05-26 16:37:17.835 9444-9466 CantDecideApp_DEBUG app.donskey.cantdecide D User MDIFs5fCHggRUd34MBUHNZGSehR2 confirmed to exist in Firestore.
2025-05-26 16:37:18.285 9444-9477 System app.donskey.cantdecide W Ignoring header X-Firebase-Locale because its value was null.
2025-05-26 16:37:18.299 9444-9477 LocalRequestInterceptor app.donskey.cantdecide W Error getting App Check token; using placeholder token instead. Error: com.google.firebase.FirebaseException: Too many attempts.
2025-05-26 16:37:18.889 9444-9477 FirebaseAuth app.donskey.cantdecide D Notifying id token listeners about user ( MDIFs5fCHggRUd34MBUHNZGSehR2 ).
2025-05-26 16:37:19.182 9444-9478 TRuntime.C...ortBackend app.donskey.cantdecide I Status Code: 200
2025-05-26 16:37:22.137 9444-9554 ProfileInstaller app.donskey.cantdecide D Installing profile for app.donskey.cantdecide
2025-05-26 16:37:26.674 9444-9521 GoogleApiManager app.donskey.cantdecide E Failed to get service from broker.
java.lang.SecurityException: Unknown calling package name 'com.google.android.gms'.
at android.os.Parcel.createExceptionOrNull(Parcel.java:3091)
at android.os.Parcel.createException(Parcel.java:3075)
at android.os.Parcel.readException(Parcel.java:3058)
at android.os.Parcel.readException(Parcel.java:3000)
at axma.a(:com.google.android.gms@251833029@25.18.33 (190400-756823100):36)
at axkh.z(:com.google.android.gms@251833029@25.18.33 (190400-756823100):143)
at awrm.run(:com.google.android.gms@251833029@25.18.33 (190400-756823100):42)
at android.os.Handler.handleCallback(Handler.java:958)
at android.os.Handler.dispatchMessage(Handler.java:99)
at cgbo.mJ(:com.google.android.gms@251833029@25.18.33 (190400-756823100):1)
at cgbo.dispatchMessage(:com.google.android.gms@251833029@25.18.33 (190400-756823100):5)
at android.os.Looper.loopOnce(Looper.java:230)
at android.os.Looper.loop(Looper.java:319)
at android.os.HandlerThread.run(HandlerThread.java:67)
