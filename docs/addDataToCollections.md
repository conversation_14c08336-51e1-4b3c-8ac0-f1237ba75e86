Eg Code of how to enter user data to users collection

import android.content.Intent;
import android.os.Bundle;
import android.widget.Button;
import android.widget.EditText;
import android.widget.Spinner;
import android.widget.Toast;
import androidx.appcompat.app.AppCompatActivity;
import com.google.firebase.auth.FirebaseAuth;
import com.google.firebase.auth.FirebaseUser;
import com.google.firebase.auth.PhoneAuthCredential;
import com.google.firebase.auth.PhoneAuthProvider;
import com.google.firebase.firestore.DocumentSnapshot;
import com.google.firebase.firestore.FirebaseFirestore;
import com.google.firebase.firestore.FieldValue;
import java.util.HashMap;
import java.util.Map;
import java.util.concurrent.TimeUnit;

public class LoginActivity extends AppCompatActivity {
private EditText phoneNumberField, codeField;
private Spinner countryCodeSpinner;
private Button sendCodeButton, verifyCodeButton;
private FirebaseAuth auth;
private FirebaseFirestore db;
private String verificationId;

    @Override
    protected void onCreate(Bundle savedInstanceState) {
        super.onCreate(savedInstanceState);
        setContentView(R.layout.activity_login);

        phoneNumberField = findViewById(R.id.phoneNumberField);
        codeField = findViewById(R.id.codeField);
        countryCodeSpinner = findViewById(R.id.countryCodeSpinner);
        sendCodeButton = findViewById(R.id.sendCodeButton);
        verifyCodeButton = findViewById(R.id.verifyCodeButton);
        auth = FirebaseAuth.getInstance();
        db = FirebaseFirestore.getInstance();

        // Set up country code spinner (you can use a library like CountryCodePicker or manually populate)
        // For simplicity, assume the spinner is populated with country codes (e.g., "+61")

        sendCodeButton.setOnClickListener(v -> {
            String countryCode = countryCodeSpinner.getSelectedItem().toString(); // e.g., "+61"
            String phoneNumber = phoneNumberField.getText().toString().trim();
            if (phoneNumber.isEmpty() || phoneNumber.length() < 9) {
                Toast.makeText(this, "Enter a valid phone number", Toast.LENGTH_SHORT).show();
                return;
            }
            String fullPhoneNumber = countryCode + phoneNumber; // e.g., "+61483331325"
            sendVerificationCode(fullPhoneNumber);
        });

        verifyCodeButton.setOnClickListener(v -> {
            String code = codeField.getText().toString().trim();
            if (code.isEmpty()) {
                Toast.makeText(this, "Enter the verification code", Toast.LENGTH_SHORT).show();
                return;
            }
            verifyCode(code);
        });
    }

    private void sendVerificationCode(String phoneNumber) {
        PhoneAuthProvider.getInstance().verifyPhoneNumber(
            phoneNumber,
            60,
            TimeUnit.SECONDS,
            this,
            new PhoneAuthProvider.OnVerificationStateChangedCallbacks() {
                @Override
                public void onVerificationCompleted(PhoneAuthCredential credential) {
                    signInWithPhoneAuthCredential(credential);
                }

                @Override
                public void onVerificationFailed(Exception e) {
                    Toast.makeText(LoginActivity.this, "Verification failed: " + e.getMessage(), Toast.LENGTH_LONG).show();
                }

                @Override
                public void onCodeSent(String vId, PhoneAuthProvider.ForceResendingToken token) {
                    verificationId = vId;
                    Toast.makeText(LoginActivity.this, "Code sent", Toast.LENGTH_SHORT).show();
                }
            }
        );
    }

    private void verifyCode(String code) {
        PhoneAuthCredential credential = PhoneAuthProvider.getCredential(verificationId, code);
        signInWithPhoneAuthCredential(credential);
    }

    private void signInWithPhoneAuthCredential(PhoneAuthCredential credential) {
        auth.signInWithCredential(credential)
            .addOnCompleteListener(this, task -> {
                if (task.isSuccessful()) {
                    Toast.makeText(this, "Sign-in successful", Toast.LENGTH_SHORT).show();
                    checkUserProfile();
                } else {
                    Toast.makeText(this, "Sign-in failed: " + task.getException().getMessage(), Toast.LENGTH_LONG).show();
                }
            });
    }

    private void checkUserProfile() {
        FirebaseUser user = auth.getCurrentUser();
        if (user != null) {
            String userId = user.getUid();
            db.collection("users")
                .document(userId)
                .get()
                .addOnCompleteListener(task -> {
                    if (task.isSuccessful()) {
                        DocumentSnapshot document = task.getResult();
                        if (document.exists()) {
                            // User has a profile, check if displayName is set
                            String displayName = document.getString("displayName");
                            if (displayName == null || displayName.isEmpty()) {
                                // Profile is incomplete, go to setup
                                startActivity(new Intent(this, ProfileSetupActivity.class));
                                finish();
                            } else {
                                // Profile is complete, go to main app
                                startActivity(new Intent(this, MainActivity.class));
                                finish();
                            }
                        } else {
                            // No profile exists, create a basic one and go to setup
                            Map<String, Object> userData = new HashMap<>();
                            userData.put("uid", userId);
                            userData.put("phoneNumber", user.getPhoneNumber());
                            userData.put("displayName", "");
                            userData.put("bio", "");
                            userData.put("email", "");
                            userData.put("photoUrl", "");
                            userData.put("allowTagging", true);
                            userData.put("createdAt", FieldValue.serverTimestamp());
                            userData.put("lastActive", FieldValue.serverTimestamp());
                            userData.put("decisionsCount", 0);
                            userData.put("friendsCount", 0);
                            userData.put("pendingRequestCount", 0);
                            userData.put("pollsCreated", 0);
                            userData.put("pollsVotedOn", 0);
                            userData.put("votesReceived", 0);
                            userData.put("fcmToken", ""); // Will be updated later with FCM token
                            userData.put("profileVisibility", "public");

                            Map<String, Object> notificationSettings = new HashMap<>();
                            notificationSettings.put("appUpdates", true);
                            notificationSettings.put("commentNotifications", true);
                            notificationSettings.put("pollInvitations", true);
                            notificationSettings.put("pollResults", true);
                            notificationSettings.put("voteNotifications", true);
                            userData.put("notificationSettings", notificationSettings);

                            Map<String, Object> themePreferences = new HashMap<>();
                            themePreferences.put("darkMode", false);
                            themePreferences.put("themeColorIndex", 0);
                            userData.put("themePreferences", themePreferences);

                            db.collection("users")
                                .document(userId)
                                .set(userData)
                                .addOnSuccessListener(aVoid -> {
                                    startActivity(new Intent(this, ProfileSetupActivity.class));
                                    finish();
                                })
                                .addOnFailureListener(e -> {
                                    Toast.makeText(this, "Error creating profile: " + e.getMessage(), Toast.LENGTH_LONG).show();
                                });
                        }
                    } else {
                        Toast.makeText(this, "Error checking profile: " + task.getException().getMessage(), Toast.LENGTH_LONG).show();
                    }
                });
        }
    }
}

Notes:
* This code creates a user document with all the fields specified, setting default values for 
  counters (decisionsCount, friendsCount, etc.) and settings (notificationSettings, themePreferences).
* The createdAt and lastActive fields use FieldValue.serverTimestamp() to set the current timestamp.
* The fcmToken field is left empty for now—we’ll update it later when we implement Firebase Cloud 
  Messaging (FCM) for notifications.
* After creating the basic document, the user is redirected to the ProfileSetupActivity to set 
  their displayName and other optional fields (as per requirements).


****************************************************************************************************

Example code of creating a friendship document in Firestore:

public void sendFriendRequest(String targetPhoneNumber) {
FirebaseUser currentUser = FirebaseAuth.getInstance().getCurrentUser();
FirebaseFirestore db = FirebaseFirestore.getInstance();

    if (currentUser == null) {
        Toast.makeText(this, "Not signed in", Toast.LENGTH_SHORT).show();
        return;
    }

    String currentUserId = currentUser.getUid();

    // First, find the target user by phone number
    db.collection("users")
        .whereEqualTo("phoneNumber", targetPhoneNumber)
        .get()
        .addOnCompleteListener(task -> {
            if (task.isSuccessful() && !task.getResult().isEmpty()) {
                DocumentSnapshot targetUserDoc = task.getResult().getDocuments().get(0);
                String targetUserId = targetUserDoc.getId();

                // Check if a friendship already exists
                db.collection("friendships")
                    .whereEqualTo("user1Id", currentUserId)
                    .whereEqualTo("user2Id", targetUserId)
                    .get()
                    .addOnCompleteListener(checkTask -> {
                        if (checkTask.isSuccessful() && checkTask.getResult().isEmpty()) {
                            // No existing friendship, create a new one
                            String friendshipId = "friendship_" + System.currentTimeMillis();
                            Map<String, Object> friendshipData = new HashMap<>();
                            friendshipData.put("friendshipId", friendshipId);
                            friendshipData.put("user1Id", currentUserId);
                            friendshipData.put("user2Id", targetUserId);
                            friendshipData.put("status", "pending");
                            friendshipData.put("initiatedBy", currentUserId);
                            friendshipData.put("createdAt", FieldValue.serverTimestamp());
                            friendshipData.put("updatedAt", FieldValue.serverTimestamp());
                            friendshipData.put("lastInteraction", FieldValue.serverTimestamp());
                            friendshipData.put("notificationEnabled", true);

                            db.collection("friendships")
                                .document(friendshipId)
                                .set(friendshipData)
                                .addOnSuccessListener(aVoid -> {
                                    Toast.makeText(this, "Friend request sent", Toast.LENGTH_SHORT).show();
                                    // Send a notification to the target user (to be implemented later)
                                })
                                .addOnFailureListener(e -> {
                                    Toast.makeText(this, "Error sending friend request: " + e.getMessage(), Toast.LENGTH_LONG).show();
                                });

                            // Update the pendingRequestCount for the target user
                            db.collection("users")
                                .document(targetUserId)
                                .update("pendingRequestCount", FieldValue.increment(1));
                        } else {
                            Toast.makeText(this, "Friend request already exists", Toast.LENGTH_SHORT).show();
                        }
                    });
            } else {
                // Target user not found, send an invite to download the app
                String appLink = "https://play.google.com/store/apps/details?id=com.example.cantdecide"; // Replace with your app's Play Store link
                String message = "Join me on Can't Decide to help me make decisions! Download the app: " + appLink;
                Intent sendIntent = new Intent(Intent.ACTION_SEND);
                sendIntent.putExtra(Intent.EXTRA_TEXT, message);
                sendIntent.setType("text/plain");
                startActivity(Intent.createChooser(sendIntent, "Invite via"));
            }
        });
}

Notes:
* This code looks up the target user by their phone number in the users collection.
* If the user exists, it creates a new friendship document with a pending status.
* If the user doesn’t exist, it sends an invite to download the app via SMS, email, or 
  another app (e.g., WhatsApp).
* It also increments the pendingRequestCount field in the target user’s document.

****************************************************************************************************

Example of how to implement phone number sign-in in your Android app

import android.os.Bundle;
import android.widget.Button;
import android.widget.EditText;
import android.widget.Toast;
import androidx.appcompat.app.AppCompatActivity;
import com.google.firebase.auth.FirebaseAuth;
import com.google.firebase.auth.PhoneAuthCredential;
import com.google.firebase.auth.PhoneAuthProvider;
import java.util.concurrent.TimeUnit;

public class LoginActivity extends AppCompatActivity {
    private EditText phoneNumberField, codeField;
    private Button sendCodeButton, verifyCodeButton;
    private FirebaseAuth auth;
    private String verificationId;

    @Override
    protected void onCreate(Bundle savedInstanceState) {
        super.onCreate(savedInstanceState);
        setContentView(R.layout.activity_login);

        phoneNumberField = findViewById(R.id.phoneNumberField);
        codeField = findViewById(R.id.codeField);
        sendCodeButton = findViewById(R.id.sendCodeButton);
        verifyCodeButton = findViewById(R.id.verifyCodeButton);
        auth = FirebaseAuth.getInstance();

        sendCodeButton.setOnClickListener(v -> {
            String phoneNumber = phoneNumberField.getText().toString().trim();
            if (phoneNumber.isEmpty() || !phoneNumber.startsWith("+")) {
                Toast.makeText(this, "Enter a valid phone number (e.g., +61483331325)", Toast.LENGTH_SHORT).show();
                return;
            }
            sendVerificationCode(phoneNumber);
        });

        verifyCodeButton.setOnClickListener(v -> {
            String code = codeField.getText().toString().trim();
            if (code.isEmpty()) {
                Toast.makeText(this, "Enter the verification code", Toast.LENGTH_SHORT).show();
                return;
            }
            verifyCode(code);
        });
    }

    private void sendVerificationCode(String phoneNumber) {
        PhoneAuthProvider.getInstance().verifyPhoneNumber(
            phoneNumber,
            60, // Timeout duration
            TimeUnit.SECONDS,
            this,
            new PhoneAuthProvider.OnVerificationStateChangedCallbacks() {
                @Override
                public void onVerificationCompleted(PhoneAuthCredential credential) {
                    // Auto-verification (e.g., if the device receives the SMS automatically)
                    signInWithPhoneAuthCredential(credential);
                }

                @Override
                public void onVerificationFailed(Exception e) {
                    Toast.makeText(LoginActivity.this, "Verification failed: " + e.getMessage(), Toast.LENGTH_LONG).show();
                }

                @Override
                public void onCodeSent(String vId, PhoneAuthProvider.ForceResendingToken token) {
                    verificationId = vId;
                    Toast.makeText(LoginActivity.this, "Code sent", Toast.LENGTH_SHORT).show();
                }
            }
        );
    }

    private void verifyCode(String code) {
        PhoneAuthCredential credential = PhoneAuthProvider.getCredential(verificationId, code);
        signInWithPhoneAuthCredential(credential);
    }

    private void signInWithPhoneAuthCredential(PhoneAuthCredential credential) {
        auth.signInWithCredential(credential)
            .addOnCompleteListener(this, task -> {
                if (task.isSuccessful()) {
                    // User is signed in, save their data to Firestore
                    saveUserToFirestore();
                    Toast.makeText(this, "Sign-in successful", Toast.LENGTH_SHORT).show();
                    // Start the main activity
                    startActivity(new Intent(this, MainActivity.class));
                    finish();
                } else {
                    Toast.makeText(this, "Sign-in failed: " + task.getException().getMessage(), Toast.LENGTH_LONG).show();
                }
            });
    }

    private void saveUserToFirestore() {
        FirebaseUser user = auth.getCurrentUser();
        if (user != null) {
            Map<String, Object> userData = new HashMap<>();
            userData.put("uid", user.getUid());
            userData.put("phoneNumber", user.getPhoneNumber()); // Automatically provided by phone auth
            userData.put("displayName", user.getDisplayName() != null ? user.getDisplayName() : "");
            userData.put("email", user.getEmail() != null ? user.getEmail() : "");
            userData.put("profileVisibility", "public");

            FirebaseFirestore.getInstance()
                .collection("users")
                .document(user.getUid())
                .set(userData)
                .addOnSuccessListener(aVoid -> Log.d("Firestore", "User profile created"))
                .addOnFailureListener(e -> Log.e("Firestore", "Error creating user profile", e));
        }
    }
}
