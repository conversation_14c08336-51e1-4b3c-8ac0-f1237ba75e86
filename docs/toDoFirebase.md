    Change Firebase rules in Storage by 19th May 2025, currently has test rules

    Make following changes to Firebase for real phone numbers after development
    is complete.

### Move to Production Mode:

    - Go to the Firebase Console → Authentication → Sign-in method → Phone
      Switch from "Test configuration" to "Production configuration"
    - ✅ Add SHA-1 Fingerprint:

### Enable App Verification:

    - This should be automatically handled when you switch to production mode
    - It uses Google Play Services to verify the device is legitimate
    - Verification Message Customization (Optional):
    - You can customize the verification SMS text in Firebase Console
    - Located in Authentication → Templates → SMS message

### Production Project Billing:

    - For production use, you need to enable billing on your Firebase project
    - Phone authentication requires a paid plan when sending to real numbers
    - Google gives you a quota of free verifications, but you need billing enabled
    - Once you've made these changes, the app will send actual SMS verification
      codes to real phone numbers instead of using the test verification code.

# Firebase TODOs

## Authentication

- **Customize Email Action Handler Pages:**
  - The default Firebase web pages shown after email actions (like verification after adding/changing email in profile) are confusing for a phone-only sign-in app.
  - Create custom web handler pages (e.g., using Firebase Hosting) with app branding and appropriate messages (e.g., "Email verified successfully. Return to the app.").
  - Update the action URLs in Firebase Console (Authentication -> Templates) to point to these custom pages.
  - Relevant action: Email address verification (used by `verifyBeforeUpdateEmail`).

Storage Rules
The current rule allows any logged-in user to read all poll images, regardless of visibility.
What Needs to Happen Later:
When you implement the poll creation and sharing features, we will need to revisit and update the storage.rules for the poll_images path. This will involve:

- Linking to Firestore: The rules will need to query your Firestore database to check information about the poll (pollId).
- Checking Creator: For allow write, the rule will need to fetch the poll document from Firestore and check if request.auth.uid matches the creatorUid stored in that document.
- Checking Visibility/Membership: For allow read, the rules will need to fetch the poll document and check its visibility status (e.g., 'public', 'private', 'friends') and potentially check if request.auth.uid is in a list of members or invitedFriends associated with that poll in Firestore.
- Adding Constraints: We should also add file size and type constraints (request.resource.size, request.resource.contentType) similar to the profile image rules.
  In Summary:
  The profile_images rules are good for now. The poll_images rules are just temporary placeholders and must be updated with more specific, Firestore-linked logic when you build the poll features to ensure only the correct users can upload and view poll images based on your app's requirements.
