Initial Setup for "View Private Polls" (Conceptual Steps from Previous Plan)

Created ViewPrivatePollsContract.kt:
Defined PollListItemUiModel to represent how a poll will look in the list.
Defined ViewPrivatePollsState (Loading, Success, Empty, Error) for MVI.
Defined ViewPrivatePollsEvent (LoadPolls, OnPollClick, OnDeletePollClick).
Defined ViewPrivatePollsEffect (NavigateToPollDetails, ShowMessage, ConfirmPollDeletion).
Created ViewPrivatePollsViewModel.kt:
Basic ViewModel structure with Hilt annotations.
Placeholder logic to fetch and manage poll data (initially with AuthRepository, later corrected).
Created ViewPrivatePollsScreen.kt:
Basic Composable structure with Scaffold, TopAppBar.
Initial logic to observe ViewState and display different UI for Loading, Success (with LazyColumn), Empty, and Error states.
Created a placeholder PollListItemCard Composable.
Updated Navigation (MainActivity.kt):
Modified the NavHost to route PollsDestinations.VIEW_PRIVATE_TEXT_POLLS to the new ViewPrivatePollsScreen.
Removed Placeholder (PollPlaceholderScreens.kt):
Deleted the old ViewPrivateTextPollsScreen placeholder Composable.
Extended Polls Repository:
PollsRepository.kt (Interface): Added new methods:
getPrivateTextPollsCreatedByCurrentUser(userId: String): Flow<List<PrivateTextPoll>>
getPrivateTextPollsInvitedToCurrentUser(userId: String): Flow<List<PrivateTextPoll>>
(Initially, these used TextPoll, later corrected to PrivateTextPoll).
PollsRepositoryImpl.kt (Implementation):
Implemented the new methods using Firestore callbackFlow for real-time updates.
Corrected the Firestore collection name to private_polls.
Implemented ViewPrivatePollsViewModel.kt Logic:
Fetched current user ID.
Combined flows from getPrivateTextPollsCreatedByCurrentUser and getPrivateTextPollsInvitedToCurrentUser.
Transformed PrivateTextPoll data models to PollListItemUiModel.
Handled loading, success, empty, and error states.
Implemented ViewPrivatePollsScreen.kt UI:
Used LazyColumn to display PollListItemUiModel from the ViewModel.
Implemented PollListItemCard to display poll details.
Handled effects for navigation and messages (Snackbar).
Use toast message as placeholder when Poll is tapped (Navigagte to details for Poll), will be 
replaced with a vote button in next session to take the user to the Poll detaisl to vote, when they return to 
View polls, the button will not appear for that user but say what they voted
Aligned createTextPoll to return Resource<String> like other new methods.
• • After these steps, the ./gradlew assembleDebug command successfully built the project.
___________________________________________________________________________________________________

T