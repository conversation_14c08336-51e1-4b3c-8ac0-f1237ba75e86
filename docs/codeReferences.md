For implementing friend-related queries, add this code to your Repository class:

// Get user's accepted friends
fun getUserFriends(userId: String) = firestore.collection("friends")
.whereEqualTo("userId", userId)
.whereEqualTo("status", "accepted")
.get()

// OR another way to get friends (when userId2 is the current user)
fun getUserFriends2(userId: String) = firestore.collection("friends")
.whereEqualTo("userId2", userId)
.whereEqualTo("status", "accepted")
.get()

// Get incoming friend requests
fun getIncomingRequests(userId: String) = firestore.collection("friends")
.whereEqualTo("userId2", userId)
.whereEqualTo("status", "pending")
.whereNotEqualTo("initiatedBy", userId)
.get()

// Get outgoing friend requests
fun getOutgoingRequests(userId: String) = firestore.collection("friends")
.whereEqualTo("userId", userId)
.whereEqualTo("status", "pending")
.whereEqualTo("initiatedBy", userId)
.get()